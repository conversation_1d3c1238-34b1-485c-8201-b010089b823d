#!/usr/bin/env python3
"""
Check Department Data in Extracted Data
Verify if department data exists in the latest extracted_data session
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_department_in_extracted_data():
    """Check if department data exists in extracted_data"""
    print("🔍 CHECKING DEPARTMENT DATA IN EXTRACTED_DATA")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Get the latest session
        latest_session = "audit_session_1751148955_7321e46d"
        
        print(f"📊 ANALYZING SESSION: {latest_session}")
        
        # 1. CHECK ALL ITEM LABELS IN LATEST SESSION
        print(f"\n1. 📋 ALL ITEM LABELS IN LATEST SESSION:")
        
        cursor.execute("""
            SELECT DISTINCT item_label, COUNT(*) as count
            FROM extracted_data
            WHERE session_id = ?
            GROUP BY item_label
            ORDER BY count DESC
        """, (latest_session,))
        
        all_labels = cursor.fetchall()
        
        department_found = False
        department_count = 0
        
        print(f"   📊 Found {len(all_labels)} different item labels:")
        for label, count in all_labels:
            print(f"      {label}: {count} records")
            
            if 'DEPARTMENT' in label.upper() or 'DEPT' in label.upper():
                department_found = True
                department_count += count
                print(f"         🎯 DEPARTMENT LABEL FOUND!")
        
        if not department_found:
            print(f"\n   ❌ NO DEPARTMENT LABELS FOUND")
            print(f"   🔍 Looking for similar labels...")
            
            # Look for labels that might contain department info
            similar_labels = []
            for label, count in all_labels:
                if any(word in label.upper() for word in ['SECTION', 'DIVISION', 'UNIT', 'OFFICE', 'BRANCH']):
                    similar_labels.append((label, count))
            
            if similar_labels:
                print(f"   📊 Similar labels found:")
                for label, count in similar_labels:
                    print(f"      {label}: {count} records")
            else:
                print(f"   ❌ No similar labels found")
        else:
            print(f"\n   ✅ DEPARTMENT DATA FOUND: {department_count} total records")
        
        # 2. CHECK SPECIFIC EMPLOYEES FOR DEPARTMENT DATA
        print(f"\n2. 📋 CHECKING SPECIFIC EMPLOYEES:")
        
        # Check some successful employees first
        test_employees = ['COP0420', 'COP1469', 'COP1665']  # Known to have departments
        
        for emp_id in test_employees:
            cursor.execute("""
                SELECT item_label, item_value
                FROM extracted_data
                WHERE session_id = ? AND employee_id = ?
                AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%')
            """, (latest_session, emp_id))
            
            dept_data = cursor.fetchall()
            if dept_data:
                print(f"   ✅ {emp_id}:")
                for label, value in dept_data:
                    print(f"      {label}: {value}")
            else:
                print(f"   ❌ {emp_id}: No department data in latest session")
                
                # Check what data they do have
                cursor.execute("""
                    SELECT DISTINCT item_label
                    FROM extracted_data
                    WHERE session_id = ? AND employee_id = ?
                    ORDER BY item_label
                """, (latest_session, emp_id))
                
                labels = [row[0] for row in cursor.fetchall()]
                print(f"      Available labels: {', '.join(labels[:5])}")
                if len(labels) > 5:
                    print(f"      ... and {len(labels) - 5} more")
        
        # 3. CHECK THE 5 MISSING EMPLOYEES
        print(f"\n3. 📋 CHECKING THE 5 MISSING EMPLOYEES:")
        
        missing_employees = ['PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617']
        
        for emp_id in missing_employees:
            cursor.execute("""
                SELECT DISTINCT item_label
                FROM extracted_data
                WHERE session_id = ? AND employee_id = ?
                ORDER BY item_label
            """, (latest_session, emp_id))
            
            labels = [row[0] for row in cursor.fetchall()]
            print(f"   📊 {emp_id}: {len(labels)} labels")
            print(f"      Labels: {', '.join(labels[:8])}")
            if len(labels) > 8:
                print(f"      ... and {len(labels) - 8} more")
            
            # Check if any label might contain department info
            dept_like_labels = [label for label in labels if any(word in label.upper() for word in ['DEPARTMENT', 'DEPT', 'SECTION', 'DIVISION'])]
            if dept_like_labels:
                print(f"      🎯 Department-like labels: {', '.join(dept_like_labels)}")
                
                # Get values for these labels
                for label in dept_like_labels:
                    cursor.execute("""
                        SELECT item_value
                        FROM extracted_data
                        WHERE session_id = ? AND employee_id = ? AND item_label = ?
                    """, (latest_session, emp_id, label))
                    
                    values = cursor.fetchall()
                    for value in values:
                        print(f"         {label}: {value[0]}")
        
        # 4. COMPARE WITH PREVIOUS SUCCESSFUL SESSIONS
        print(f"\n4. 📋 COMPARING WITH PREVIOUS SUCCESSFUL SESSIONS:")
        
        # Check the session with most department data
        successful_session = "phased_audit_20250623_175816_313201"  # Has 4907 employees with departments
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as employees_with_dept
            FROM extracted_data
            WHERE session_id = ? 
            AND item_label = 'DEPARTMENT'
            AND item_value IS NOT NULL 
            AND item_value != ''
        """, (successful_session,))
        
        old_dept_count = cursor.fetchone()[0]
        print(f"   📊 Previous successful session ({successful_session}):")
        print(f"      Employees with department: {old_dept_count}")
        
        # Check if the same employees have department data in the old session
        for emp_id in test_employees:
            cursor.execute("""
                SELECT item_value
                FROM extracted_data
                WHERE session_id = ? AND employee_id = ? AND item_label = 'DEPARTMENT'
            """, (successful_session, emp_id))
            
            result = cursor.fetchone()
            if result:
                print(f"      ✅ {emp_id}: {result[0]} (in old session)")
            else:
                print(f"      ❌ {emp_id}: No department in old session either")
        
        # 5. FINAL ASSESSMENT
        print(f"\n5. 🎯 FINAL ASSESSMENT:")
        print("=" * 50)
        
        if department_found:
            print(f"✅ Department data EXISTS in latest session")
            print(f"   📊 {department_count} department records found")
            print(f"   🎯 Issue: Department extraction is PARTIAL, not complete")
        else:
            print(f"❌ NO department data in latest session")
            print(f"   🎯 Issue: Department extraction COMPLETELY FAILED")
        
        print(f"\n📊 COMPARISON:")
        print(f"   Latest session: {department_count if department_found else 0} employees with departments")
        print(f"   Previous session: {old_dept_count} employees with departments")
        
        if department_found and department_count < old_dept_count:
            print(f"   🎯 CONCLUSION: Department extraction quality DEGRADED")
            print(f"   📋 RECOMMENDATION: Use previous session data for department lookup")
        elif not department_found:
            print(f"   🎯 CONCLUSION: Department extraction COMPLETELY FAILED in latest session")
            print(f"   📋 RECOMMENDATION: Must use previous session data for departments")
        
        conn.close()
        return department_found
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_department_in_extracted_data()
    sys.exit(0 if success else 1)
