#!/usr/bin/env python3
"""
Investigate Name Extraction Issues
Check for field misalignment and data corruption in employee names
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_name_issues():
    """Investigate name extraction issues"""
    print("🔍 INVESTIGATING NAME EXTRACTION ISSUES")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Latest session
        latest_session = "audit_session_1751148955_7321e46d"
        
        # 1. CHECK PW0101 SPECIFIC ISSUE
        print("1. 📋 CHECKING PW0101 NAME CORRUPTION:")
        
        cursor.execute("""
            SELECT item_label, item_value
            FROM extracted_data
            WHERE session_id = ? AND employee_id = 'PW0101'
            ORDER BY item_label
        """, (latest_session,))
        
        pw0101_data = cursor.fetchall()
        
        print(f"   🔍 PW0101 ALL EXTRACTED DATA:")
        for label, value in pw0101_data:
            if 'NAME' in label.upper() or 'GHANA' in label.upper():
                print(f"      🎯 {label}: {value}")
            else:
                print(f"         {label}: {value}")
        
        # 2. FIND ALL EMPLOYEES WITH "GHANA CARD" IN NAME
        print(f"\n2. 📋 EMPLOYEES WITH 'GHANA CARD' IN NAME:")
        
        cursor.execute("""
            SELECT DISTINCT employee_id, item_value
            FROM extracted_data
            WHERE session_id = ? 
            AND item_label = 'EMPLOYEE NAME'
            AND item_value LIKE '%GHANA%'
            ORDER BY employee_id
        """, (latest_session,))
        
        ghana_card_names = cursor.fetchall()
        
        if ghana_card_names:
            print(f"   ❌ FOUND {len(ghana_card_names)} EMPLOYEES WITH CORRUPTED NAMES:")
            for emp_id, name in ghana_card_names:
                print(f"      {emp_id}: {name}")
        else:
            print(f"   ✅ No employees with 'Ghana Card' in name field")
        
        # 3. FIND EMPLOYEES WITH JOB TITLES IN NAME FIELD
        print(f"\n3. 📋 EMPLOYEES WITH JOB TITLES IN NAME FIELD:")
        
        # Common job title keywords
        job_title_keywords = [
            'OFFICER', 'MANAGER', 'DIRECTOR', 'ASSISTANT', 'PRINCIPAL', 
            'SECRETARY', 'CLERK', 'SUPERVISOR', 'COORDINATOR', 'SPECIALIST',
            'ANALYST', 'TECHNICIAN', 'ENGINEER', 'ACCOUNTANT', 'AUDITOR',
            'RETIRED', 'MINISTERS', 'PASTOR', 'EVANGELIST'
        ]
        
        job_title_names = []
        
        for keyword in job_title_keywords:
            cursor.execute("""
                SELECT DISTINCT employee_id, item_value
                FROM extracted_data
                WHERE session_id = ? 
                AND item_label = 'EMPLOYEE NAME'
                AND item_value LIKE ?
                ORDER BY employee_id
            """, (latest_session, f'%{keyword}%'))
            
            results = cursor.fetchall()
            job_title_names.extend(results)
        
        # Remove duplicates
        unique_job_title_names = list(set(job_title_names))
        
        if unique_job_title_names:
            print(f"   ❌ FOUND {len(unique_job_title_names)} EMPLOYEES WITH JOB TITLES IN NAME:")
            for emp_id, name in unique_job_title_names[:10]:  # Show first 10
                print(f"      {emp_id}: {name}")
            if len(unique_job_title_names) > 10:
                print(f"      ... and {len(unique_job_title_names) - 10} more")
        else:
            print(f"   ✅ No employees with job titles in name field")
        
        # 4. CHECK FIELD MISALIGNMENT PATTERNS
        print(f"\n4. 📋 CHECKING FIELD MISALIGNMENT PATTERNS:")
        
        # Look for cases where name and other fields are swapped
        cursor.execute("""
            SELECT employee_id, item_value as name
            FROM extracted_data
            WHERE session_id = ? 
            AND item_label = 'EMPLOYEE NAME'
            AND (
                item_value LIKE 'GHA-%' OR
                item_value LIKE '%CARD%' OR
                item_value LIKE '%ID%' OR
                item_value LIKE '%NO.%' OR
                item_value LIKE '%ACCOUNT%' OR
                item_value LIKE '%BANK%'
            )
            ORDER BY employee_id
            LIMIT 20
        """, (latest_session,))
        
        misaligned_names = cursor.fetchall()
        
        if misaligned_names:
            print(f"   ❌ FOUND {len(misaligned_names)} FIELD MISALIGNMENT CASES:")
            for emp_id, name in misaligned_names:
                print(f"      {emp_id}: {name}")
                
                # Check what their actual name might be
                cursor.execute("""
                    SELECT item_label, item_value
                    FROM extracted_data
                    WHERE session_id = ? AND employee_id = ?
                    AND item_label IN ('JOB TITLE', 'GHANA CARD ID', 'SSF NO.')
                """, (latest_session, emp_id))
                
                other_fields = cursor.fetchall()
                for label, value in other_fields:
                    if any(char.isalpha() for char in value) and len(value.split()) >= 2:
                        print(f"         Possible actual name in {label}: {value}")
        else:
            print(f"   ✅ No obvious field misalignment detected")
        
        # 5. ANALYZE EXTRACTION QUALITY
        print(f"\n5. 📋 EXTRACTION QUALITY ANALYSIS:")
        
        # Total employees
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total
            FROM extracted_data
            WHERE session_id = ?
        """, (latest_session,))
        
        total_employees = cursor.fetchone()[0]
        
        # Employees with proper names (2+ words, mostly alphabetic)
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as proper_names
            FROM extracted_data
            WHERE session_id = ? 
            AND item_label = 'EMPLOYEE NAME'
            AND LENGTH(item_value) - LENGTH(REPLACE(item_value, ' ', '')) >= 1
            AND item_value NOT LIKE '%CARD%'
            AND item_value NOT LIKE '%ID%'
            AND item_value NOT LIKE '%NO.%'
        """, (latest_session,))
        
        proper_names = cursor.fetchone()[0]
        
        quality_rate = (proper_names / total_employees) * 100 if total_employees > 0 else 0
        
        print(f"   📊 Total employees: {total_employees}")
        print(f"   📊 Proper names: {proper_names}")
        print(f"   📊 Name quality rate: {quality_rate:.1f}%")
        
        # 6. RECOMMENDATIONS
        print(f"\n6. 🎯 RECOMMENDATIONS:")
        print("=" * 50)
        
        issues_found = len(ghana_card_names) + len(unique_job_title_names) + len(misaligned_names)
        
        if issues_found > 0:
            print(f"❌ CRITICAL: {issues_found} name extraction issues found")
            print(f"   📋 IMMEDIATE ACTIONS:")
            print(f"      1. Fix extractor field alignment logic")
            print(f"      2. Improve label-value pairing accuracy")
            print(f"      3. Add validation for name field content")
            print(f"      4. Re-run extraction with fixes")
            
            print(f"\n   🔧 SPECIFIC FIXES NEEDED:")
            if ghana_card_names:
                print(f"      - Fix Ghana Card ID misalignment ({len(ghana_card_names)} cases)")
            if unique_job_title_names:
                print(f"      - Fix job title misalignment ({len(unique_job_title_names)} cases)")
            if misaligned_names:
                print(f"      - Fix general field misalignment ({len(misaligned_names)} cases)")
        else:
            print(f"✅ GOOD: No major name extraction issues detected")
            print(f"   📊 {quality_rate:.1f}% name quality rate is acceptable")
        
        conn.close()
        return issues_found == 0
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = investigate_name_issues()
    sys.exit(0 if success else 1)
