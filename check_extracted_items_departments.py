#!/usr/bin/env python3
"""
Check Extracted Items Departments
Check if the 3 COP employees have department data in extracted_items table
"""

import os
import sys
import sqlite3

def get_database_path():
    """Get the database path"""
    return r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"

def check_extracted_items_departments():
    """Check if COP employees have department data in extracted_items"""
    print("🔍 CHECKING EXTRACTED_ITEMS FOR DEPARTMENT DATA")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK EXTRACTED_ITEMS TABLE STRUCTURE
        print("1. 📋 EXTRACTED_ITEMS TABLE STRUCTURE:")
        
        cursor.execute("PRAGMA table_info(extracted_items)")
        columns = cursor.fetchall()
        
        print("   📊 Columns:")
        for col_id, col_name, col_type, not_null, default_val, primary_key in columns:
            print(f"      {col_name}: {col_type}")
        
        # 2. CHECK TOTAL RECORDS AND DEPARTMENT RECORDS
        print(f"\n2. 📋 DEPARTMENT DATA IN EXTRACTED_ITEMS:")
        
        cursor.execute("SELECT COUNT(*) FROM extracted_items")
        total_records = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM extracted_items 
            WHERE item_label = 'DEPARTMENT'
        """)
        dept_records = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) FROM extracted_items 
            WHERE item_label = 'DEPARTMENT'
        """)
        employees_with_dept = cursor.fetchone()[0]
        
        print(f"   📊 Total records: {total_records:,}")
        print(f"   📊 Department records: {dept_records:,}")
        print(f"   📊 Employees with departments: {employees_with_dept:,}")
        
        # 3. CHECK THE 3 COP EMPLOYEES SPECIFICALLY
        print(f"\n3. 📋 CHECKING 3 COP EMPLOYEES:")
        
        target_employees = ['COP2894', 'COP3195', 'COP3617']
        
        for emp_id in target_employees:
            print(f"\n   🔍 {emp_id}:")
            
            # Check if employee exists at all
            cursor.execute("""
                SELECT COUNT(*) FROM extracted_items
                WHERE employee_id = ?
            """, (emp_id,))
            
            total_records = cursor.fetchone()[0]
            print(f"      📊 Total records: {total_records}")
            
            if total_records > 0:
                # Check for department specifically
                cursor.execute("""
                    SELECT item_value FROM extracted_items
                    WHERE employee_id = ? AND item_label = 'DEPARTMENT'
                """, (emp_id,))
                
                dept_results = cursor.fetchall()
                
                if dept_results:
                    print(f"      ✅ DEPARTMENT records found: {len(dept_results)}")
                    for dept_value, in dept_results:
                        print(f"         📋 DEPARTMENT: {dept_value}")
                else:
                    print(f"      ❌ NO DEPARTMENT records found")
                    
                    # Check what labels they do have
                    cursor.execute("""
                        SELECT DISTINCT item_label FROM extracted_items
                        WHERE employee_id = ?
                        ORDER BY item_label
                    """, (emp_id,))
                    
                    labels = [row[0] for row in cursor.fetchall()]
                    print(f"      📋 Available labels: {', '.join(labels[:10])}")
                    if len(labels) > 10:
                        print(f"         ... and {len(labels) - 10} more")
                    
                    # Check for department-like labels
                    dept_like_labels = [label for label in labels if 'DEPT' in label.upper() or 'SECTION' in label.upper()]
                    if dept_like_labels:
                        print(f"      🔍 Department-like labels: {', '.join(dept_like_labels)}")
                        
                        for label in dept_like_labels:
                            cursor.execute("""
                                SELECT item_value FROM extracted_items
                                WHERE employee_id = ? AND item_label = ?
                            """, (emp_id, label))
                            
                            values = cursor.fetchall()
                            for value, in values:
                                print(f"         {label}: {value}")
            else:
                print(f"      ❌ Employee not found in extracted_items")
        
        # 4. CHECK IF DEPARTMENT DATA EXISTS FOR OTHER EMPLOYEES
        print(f"\n4. 📋 SAMPLE DEPARTMENT DATA FROM OTHER EMPLOYEES:")
        
        cursor.execute("""
            SELECT employee_id, item_value FROM extracted_items
            WHERE item_label = 'DEPARTMENT'
            AND item_value IS NOT NULL
            AND item_value != ''
            ORDER BY RANDOM()
            LIMIT 10
        """)
        
        sample_depts = cursor.fetchall()
        
        if sample_depts:
            print(f"   📊 Sample departments from other employees:")
            for emp_id, dept in sample_depts:
                print(f"      {emp_id}: {dept}")
        else:
            print(f"   ❌ No department data found for any employees")
        
        # 5. FINAL RECOMMENDATION
        print(f"\n5. 🎯 RECOMMENDATION:")
        print("=" * 50)
        
        if dept_records > 0:
            coverage = (employees_with_dept / 5895) * 100
            print(f"✅ DEPARTMENT DATA EXISTS in extracted_items")
            print(f"   📊 Coverage: {employees_with_dept}/5895 employees ({coverage:.1f}%)")
            
            # Check if our 3 employees are among the missing
            missing_count = sum(1 for emp_id in target_employees 
                              if not any(emp_id == emp for emp, _ in sample_depts))
            
            if missing_count == 3:
                print(f"❌ ALL 3 COP employees missing department data")
                print(f"   🔧 These employees may have:")
                print(f"      - Department in different field (SECTION, UNIT)")
                print(f"      - Department in different format")
                print(f"      - No department assigned")
            else:
                print(f"⚠️ SOME COP employees missing department data")
        else:
            print(f"❌ NO DEPARTMENT DATA in extracted_items")
            print(f"   🔧 Need to check extraction process")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_extracted_items_departments()
    sys.exit(0 if success else 1)
