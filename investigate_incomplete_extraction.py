#!/usr/bin/env python3
"""
Investigate Incomplete Extraction
Check if extraction was incomplete for certain periods or employee groups
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_incomplete_extraction():
    """Investigate if extraction was incomplete for certain employees"""
    print("🔍 INVESTIGATING INCOMPLETE EXTRACTION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # The 5 employees missing department data
        missing_employees = ['PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617']
        
        print(f"📊 INVESTIGATING 5 EMPLOYEES WITH MISSING DEPARTMENTS:")
        for emp_id in missing_employees:
            print(f"   {emp_id}")
        
        # 1. CHECK EXTRACTION SESSIONS FOR THESE EMPLOYEES
        print(f"\n1. 📋 CHECKING EXTRACTION SESSIONS:")
        
        for emp_id in missing_employees:
            print(f"\n   🔍 {emp_id}:")
            
            # Check if employee exists in any extraction session
            cursor.execute("""
                SELECT DISTINCT session_id, COUNT(*) as records
                FROM extracted_data 
                WHERE employee_id = ?
                GROUP BY session_id
                ORDER BY records DESC
            """, (emp_id,))
            
            sessions = cursor.fetchall()
            if sessions:
                print(f"      ✅ Found in extracted_data:")
                for session_id, count in sessions:
                    print(f"         {session_id}: {count} records")
                    
                    # Check what data was extracted
                    cursor.execute("""
                        SELECT DISTINCT item_label
                        FROM extracted_data 
                        WHERE employee_id = ? AND session_id = ?
                        ORDER BY item_label
                    """, (emp_id, session_id))
                    
                    labels = [row[0] for row in cursor.fetchall()]
                    print(f"         Labels: {', '.join(labels[:10])}")
                    if len(labels) > 10:
                        print(f"         ... and {len(labels) - 10} more")
                    
                    # Specifically check for department-related labels
                    dept_labels = [label for label in labels if 'DEPARTMENT' in label.upper() or 'DEPT' in label.upper()]
                    if dept_labels:
                        print(f"         🎯 Department labels: {', '.join(dept_labels)}")
                        
                        # Get the actual department values
                        for dept_label in dept_labels:
                            cursor.execute("""
                                SELECT item_value
                                FROM extracted_data 
                                WHERE employee_id = ? AND session_id = ? AND item_label = ?
                            """, (emp_id, session_id, dept_label))
                            
                            values = cursor.fetchall()
                            for value in values:
                                print(f"            {dept_label}: {value[0]}")
                    else:
                        print(f"         ❌ No department labels found")
            else:
                print(f"      ❌ Not found in extracted_data at all")
        
        # 2. CHECK IF THESE EMPLOYEES HAVE DIFFERENT EXTRACTION PATTERNS
        print(f"\n2. 📋 CHECKING EXTRACTION PATTERNS:")
        
        # Compare with successful employees
        successful_employees = ['COP0420', 'COP1469', 'COP1665', 'COP1716', 'COP1770']
        
        print(f"\n   📊 COMPARISON WITH SUCCESSFUL EMPLOYEES:")
        
        for emp_id in successful_employees[:2]:  # Check first 2 successful
            print(f"\n   ✅ {emp_id} (SUCCESSFUL):")
            
            cursor.execute("""
                SELECT session_id, COUNT(*) as records
                FROM extracted_data 
                WHERE employee_id = ?
                GROUP BY session_id
                ORDER BY records DESC
                LIMIT 1
            """, (emp_id,))
            
            result = cursor.fetchone()
            if result:
                session_id, count = result
                print(f"      Session: {session_id}, Records: {count}")
                
                # Check for department data
                cursor.execute("""
                    SELECT item_label, item_value
                    FROM extracted_data 
                    WHERE employee_id = ? AND session_id = ?
                    AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%')
                """, (emp_id, session_id))
                
                dept_data = cursor.fetchall()
                for label, value in dept_data:
                    print(f"      {label}: {value}")
        
        # 3. CHECK EXTRACTION COMPLETENESS BY EMPLOYEE ID PATTERNS
        print(f"\n3. 📋 CHECKING EXTRACTION BY EMPLOYEE ID PATTERNS:")
        
        # Check PW employees
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total,
                   COUNT(DISTINCT CASE WHEN item_label = 'DEPARTMENT' THEN employee_id END) as with_dept
            FROM extracted_data 
            WHERE employee_id LIKE 'PW%'
        """)
        
        pw_stats = cursor.fetchone()
        if pw_stats:
            total, with_dept = pw_stats
            print(f"   📊 PW employees: {with_dept}/{total} have department data ({with_dept/total*100:.1f}%)")
        
        # Check COP employees in the 2800-3700 range (where our missing employees are)
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total,
                   COUNT(DISTINCT CASE WHEN item_label = 'DEPARTMENT' THEN employee_id END) as with_dept
            FROM extracted_data 
            WHERE employee_id LIKE 'COP%'
            AND CAST(SUBSTR(employee_id, 4) AS INTEGER) BETWEEN 2800 AND 3700
        """)
        
        cop_high_stats = cursor.fetchone()
        if cop_high_stats:
            total, with_dept = cop_high_stats
            print(f"   📊 COP2800-3700 employees: {with_dept}/{total} have department data ({with_dept/total*100:.1f}%)")
        
        # 4. CHECK IF THESE EMPLOYEES ARE IN DIFFERENT PERIODS
        print(f"\n4. 📋 CHECKING PERIOD EXTRACTION:")
        
        # Check if there are different period types
        cursor.execute("""
            SELECT DISTINCT period_type, COUNT(DISTINCT employee_id) as employees
            FROM employees
            GROUP BY period_type
        """)
        
        periods = cursor.fetchall()
        if periods:
            print(f"   📊 Period types in employees table:")
            for period, count in periods:
                print(f"      {period}: {count} employees")
        
        # Check if missing employees are in a different period
        for emp_id in missing_employees:
            cursor.execute("""
                SELECT DISTINCT period_type
                FROM employees
                WHERE employee_id = ?
            """, (emp_id,))
            
            emp_periods = cursor.fetchall()
            if emp_periods:
                periods_str = ', '.join([p[0] for p in emp_periods])
                print(f"   📋 {emp_id}: periods = {periods_str}")
        
        # 5. FINAL RECOMMENDATION
        print(f"\n5. 🎯 EXTRACTION COMPLETENESS ANALYSIS:")
        print("=" * 50)
        
        # Check total extraction coverage
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total_extracted
            FROM extracted_data
        """)
        total_extracted = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total_employees
            FROM employees
        """)
        total_employees = cursor.fetchone()[0]
        
        print(f"   📊 Total employees extracted: {total_extracted}")
        print(f"   📊 Total employees in database: {total_employees}")
        
        if total_extracted < total_employees:
            print(f"   ⚠️ EXTRACTION INCOMPLETE: {total_employees - total_extracted} employees missing from extraction")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = investigate_incomplete_extraction()
    sys.exit(0 if success else 1)
