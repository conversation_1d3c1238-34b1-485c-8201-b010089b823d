#!/usr/bin/env python3
"""
Check PDF Page Count
Verify the actual number of pages in the PDF files to confirm if the issue is file-based or extraction-based
"""

import os
import sys
from pathlib import Path

def check_pdf_pages():
    """Check the actual page count of PDF files"""
    print("🔍 CHECKING PDF FILE PAGE COUNTS")
    print("=" * 60)
    
    try:
        # Try different PDF libraries
        pdf_info = {}
        
        # Method 1: PyPDF2
        try:
            import PyPDF2
            print("✅ PyPDF2 available")
            
            # Check actual PDF locations
            pdf_paths = [
                "payslips/JULY.pdf",
                "payslips/JUNE.pdf",
                "JULY.pdf",
                "JUNE.pdf"
            ]
            
            for pdf_path in pdf_paths:
                if os.path.exists(pdf_path):
                    try:
                        with open(pdf_path, 'rb') as file:
                            pdf_reader = PyPDF2.PdfReader(file)
                            page_count = len(pdf_reader.pages)
                            file_size = os.path.getsize(pdf_path) / (1024 * 1024)  # MB
                            
                            pdf_info[pdf_path] = {
                                'pages': page_count,
                                'size_mb': file_size,
                                'method': 'PyPDF2'
                            }
                            
                            print(f"\n📄 {pdf_path}:")
                            print(f"   Pages: {page_count}")
                            print(f"   Size: {file_size:.1f} MB")
                            
                            # Check if this matches our expected counts
                            if 'JUNE' in pdf_path.upper() and abs(page_count - 2959) < 100:
                                print(f"   ✅ JUNE file matches expected ~2959 pages")
                            elif 'JULY' in pdf_path.upper() and abs(page_count - 2960) < 100:
                                print(f"   ✅ JULY file matches expected ~2960 pages")
                            elif page_count > 5000:
                                print(f"   🎯 COMBINED file with {page_count} pages")
                            elif page_count < 3000:
                                print(f"   ⚠️ PARTIAL file - only {page_count} pages")
                            
                    except Exception as e:
                        print(f"   ❌ Error reading {pdf_path} with PyPDF2: {e}")
        
        except ImportError:
            print("❌ PyPDF2 not available")
        
        # Method 2: Try fitz (PyMuPDF)
        try:
            import fitz
            print("\n✅ PyMuPDF (fitz) available")
            
            for pdf_path in pdf_paths:
                if os.path.exists(pdf_path) and pdf_path not in pdf_info:
                    try:
                        doc = fitz.open(pdf_path)
                        page_count = len(doc)
                        doc.close()
                        
                        file_size = os.path.getsize(pdf_path) / (1024 * 1024)  # MB
                        
                        pdf_info[pdf_path] = {
                            'pages': page_count,
                            'size_mb': file_size,
                            'method': 'PyMuPDF'
                        }
                        
                        print(f"\n📄 {pdf_path}:")
                        print(f"   Pages: {page_count}")
                        print(f"   Size: {file_size:.1f} MB")
                        
                    except Exception as e:
                        print(f"   ❌ Error reading {pdf_path} with PyMuPDF: {e}")
        
        except ImportError:
            print("❌ PyMuPDF not available")
        
        # Summary analysis
        print(f"\n📊 SUMMARY ANALYSIS:")
        print("=" * 50)
        
        if not pdf_info:
            print("❌ No PDF files found or readable")
            print("📋 Expected locations:")
            for path in pdf_paths:
                exists = "✅" if os.path.exists(path) else "❌"
                print(f"   {exists} {path}")
            return False
        
        total_pages = 0
        for pdf_path, info in pdf_info.items():
            total_pages += info['pages']
            print(f"📄 {pdf_path}: {info['pages']} pages ({info['size_mb']:.1f} MB)")
        
        print(f"\n🎯 TOTAL PAGES AVAILABLE: {total_pages}")
        print(f"📊 EXPECTED TOTAL: 5919 (2960 July + 2959 June)")
        
        if total_pages >= 5919:
            print(f"✅ PDF files contain enough pages ({total_pages} >= 5919)")
            print(f"🎯 CONCLUSION: The issue is in the EXTRACTION PROCESS, not the PDF files")
        elif total_pages >= 2900:
            print(f"⚠️ PDF files contain {total_pages} pages (partial)")
            print(f"🎯 CONCLUSION: PDF files may be incomplete or split")
        else:
            print(f"❌ PDF files contain only {total_pages} pages")
            print(f"🎯 CONCLUSION: PDF files are definitely incomplete")
        
        # Check for specific patterns
        print(f"\n🔍 PATTERN ANALYSIS:")
        
        # Check if we have exactly the extracted amount
        if any(info['pages'] == 2967 for info in pdf_info.values()):
            print(f"🎯 FOUND: One PDF has exactly 2967 pages (matches extraction)")
            print(f"   This suggests the PDF file itself is limited, not the extraction")
        
        # Check if we have the expected amounts
        june_found = any('JUNE' in path.upper() and abs(info['pages'] - 2959) < 100 
                        for path, info in pdf_info.items())
        july_found = any('JULY' in path.upper() and abs(info['pages'] - 2960) < 100 
                        for path, info in pdf_info.items())
        
        if june_found and july_found:
            print(f"✅ Both JUNE (~2959) and JULY (~2960) files found with correct page counts")
        elif june_found:
            print(f"✅ JUNE file found with ~2959 pages")
        elif july_found:
            print(f"✅ JULY file found with ~2960 pages")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_pdf_pages()
    sys.exit(0 if success else 1)
