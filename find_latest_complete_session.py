#!/usr/bin/env python3
"""
Find Latest Complete Session
Find the actual latest session from the completed extraction
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def find_latest_complete_session():
    """Find the actual latest complete session"""
    print("🔍 FINDING LATEST COMPLETE SESSION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK ALL SESSIONS BY CREATION TIME
        print("1. 📋 ALL SESSIONS BY LATEST CREATION TIME:")
        
        cursor.execute("""
            SELECT session_id, 
                   COUNT(DISTINCT employee_id) as employees,
                   COUNT(CASE WHEN item_label = 'DEPARTMENT' THEN 1 END) as dept_records,
                   MAX(created_at) as latest_time
            FROM extracted_data
            GROUP BY session_id
            ORDER BY latest_time DESC
            LIMIT 10
        """)
        
        all_sessions = cursor.fetchall()
        
        print(f"   📊 Sessions ordered by latest creation time:")
        for session_id, employees, dept_records, latest_time in all_sessions:
            status = "✅ COMPLETE" if employees >= 5800 else "⚠️ PARTIAL" if employees >= 2900 else "❌ INCOMPLETE"
            print(f"      {status} {session_id}")
            print(f"         Employees: {employees}, Departments: {dept_records}, Time: {latest_time}")
        
        # Find the most recent complete session
        latest_complete = None
        for session_id, employees, dept_records, latest_time in all_sessions:
            if employees >= 5800:  # Complete extraction
                latest_complete = session_id
                break
        
        if not latest_complete:
            print(f"\n   ❌ No complete sessions found (5800+ employees)")
            # Use the most recent session regardless
            latest_complete = all_sessions[0][0] if all_sessions else None
        
        if not latest_complete:
            print(f"   ❌ No sessions found at all")
            return False
        
        print(f"\n   🎯 Using session: {latest_complete}")
        
        # 2. CHECK THE 5 SPECIFIC EMPLOYEES IN LATEST SESSION
        print(f"\n2. 📋 CHECKING THE 5 EMPLOYEES IN LATEST SESSION:")
        
        target_employees = ['PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617']
        
        for emp_id in target_employees:
            print(f"\n   🔍 {emp_id}:")
            
            # Check if employee exists at all
            cursor.execute("""
                SELECT COUNT(*) as record_count
                FROM extracted_data
                WHERE session_id = ? AND employee_id = ?
            """, (latest_complete, emp_id))
            
            record_count = cursor.fetchone()[0]
            
            if record_count > 0:
                print(f"      ✅ EXISTS: {record_count} records")
                
                # Check name
                cursor.execute("""
                    SELECT item_value
                    FROM extracted_data
                    WHERE session_id = ? AND employee_id = ? AND item_label = 'EMPLOYEE NAME'
                """, (latest_complete, emp_id))
                
                name_result = cursor.fetchone()
                if name_result:
                    print(f"      📋 NAME: {name_result[0]}")
                
                # Check department
                cursor.execute("""
                    SELECT item_value
                    FROM extracted_data
                    WHERE session_id = ? AND employee_id = ? AND item_label = 'DEPARTMENT'
                """, (latest_complete, emp_id))
                
                dept_result = cursor.fetchone()
                if dept_result:
                    print(f"      📋 DEPARTMENT: {dept_result[0]}")
                else:
                    print(f"      ❌ DEPARTMENT: Not found")
                    
                    # Check what labels they do have
                    cursor.execute("""
                        SELECT DISTINCT item_label
                        FROM extracted_data
                        WHERE session_id = ? AND employee_id = ?
                        ORDER BY item_label
                    """, (latest_complete, emp_id))
                    
                    labels = [row[0] for row in cursor.fetchall()]
                    print(f"      📋 Available labels: {', '.join(labels[:10])}")
            else:
                print(f"      ❌ NOT FOUND in latest session")
        
        # 3. CHECK OVERALL STATISTICS
        print(f"\n3. 📋 OVERALL STATISTICS FOR LATEST SESSION:")
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total_employees
            FROM extracted_data
            WHERE session_id = ?
        """, (latest_complete,))
        
        total_employees = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as employees_with_dept
            FROM extracted_data
            WHERE session_id = ? AND item_label = 'DEPARTMENT'
        """, (latest_complete,))
        
        employees_with_dept = cursor.fetchone()[0]
        
        print(f"   📊 Total employees: {total_employees}")
        print(f"   📊 With departments: {employees_with_dept}")
        print(f"   📊 Department coverage: {employees_with_dept/total_employees*100:.1f}%")
        
        if total_employees >= 5800:
            print(f"   ✅ COMPLETE EXTRACTION (expected ~5919)")
        else:
            print(f"   ⚠️ INCOMPLETE EXTRACTION (expected ~5919)")
        
        conn.close()
        return latest_complete
        
    except Exception as e:
        print(f"❌ Search failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    session = find_latest_complete_session()
    if session:
        print(f"\n🎯 LATEST COMPLETE SESSION: {session}")
    else:
        print(f"\n❌ NO COMPLETE SESSION FOUND")
    sys.exit(0)
