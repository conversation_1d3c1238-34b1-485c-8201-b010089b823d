#!/usr/bin/env python3
"""
Verify Actual Payslip Counts
Check if extraction captured the correct numbers: 2960 July + 2959 June = 5919 total
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def verify_payslip_counts():
    """Verify if extraction captured the correct payslip counts"""
    print("🔍 VERIFYING ACTUAL PAYSLIP COUNTS")
    print("=" * 60)
    print("📊 EXPECTED: July = 2960, June = 2959, Total = 5919")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK TOTAL EXTRACTED EMPLOYEES
        print(f"1. 📋 TOTAL EXTRACTION VERIFICATION:")
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total_employees
            FROM extracted_data
        """)
        
        total_extracted = cursor.fetchone()[0]
        print(f"   📊 Total employees extracted: {total_extracted}")
        print(f"   📊 Expected total: 5919 (2960 + 2959)")
        print(f"   📊 Difference: {5919 - total_extracted} ({((5919 - total_extracted)/5919)*100:.1f}% missing)")
        
        # 2. CHECK PERIOD-SPECIFIC EXTRACTION
        print(f"\n2. 📋 PERIOD-SPECIFIC VERIFICATION:")
        
        # Method 1: Check by period_type in employees table
        cursor.execute("""
            SELECT period_type, COUNT(DISTINCT employee_id) as employees
            FROM employees
            GROUP BY period_type
        """)
        
        period_types = cursor.fetchall()
        print(f"\n   📊 By period_type in employees table:")
        for period, count in period_types:
            print(f"      {period}: {count} employees")
        
        # Method 2: Check by month indicators in extracted data
        print(f"\n   📊 By month indicators in extracted_data:")
        
        # July indicators (07, July)
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id)
            FROM extracted_data
            WHERE item_value LIKE '%07%' OR item_value LIKE '%july%' OR item_value LIKE '%JULY%'
        """)
        july_count = cursor.fetchone()[0]
        
        # June indicators (06, June)  
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id)
            FROM extracted_data
            WHERE item_value LIKE '%06%' OR item_value LIKE '%june%' OR item_value LIKE '%JUNE%'
        """)
        june_count = cursor.fetchone()[0]
        
        print(f"      July indicators: {july_count} employees (expected: 2960)")
        print(f"      June indicators: {june_count} employees (expected: 2959)")
        
        # 3. CHECK FOR DUAL PERIOD EMPLOYEES
        print(f"\n3. 📋 DUAL PERIOD ANALYSIS:")
        
        # Find employees with both June and July data
        cursor.execute("""
            SELECT employee_id
            FROM extracted_data
            WHERE item_value LIKE '%07%' OR item_value LIKE '%july%' OR item_value LIKE '%JULY%'
            INTERSECT
            SELECT employee_id
            FROM extracted_data
            WHERE item_value LIKE '%06%' OR item_value LIKE '%june%' OR item_value LIKE '%JUNE%'
        """)
        
        dual_period_employees = cursor.fetchall()
        dual_count = len(dual_period_employees)
        
        print(f"   📊 Employees with BOTH periods: {dual_count}")
        print(f"   📊 July-only employees: {july_count - dual_count}")
        print(f"   📊 June-only employees: {june_count - dual_count}")
        
        # 4. CHECK EXTRACTION COMPLETENESS
        print(f"\n4. 📋 EXTRACTION COMPLETENESS ANALYSIS:")
        
        expected_july = 2960
        expected_june = 2959
        expected_total = 5919
        
        # If we have dual period data, total unique should be less than sum
        if dual_count > 0:
            expected_unique = expected_july + expected_june - dual_count
            print(f"   📊 Expected unique employees (accounting for dual): {expected_unique}")
        else:
            expected_unique = expected_total
            print(f"   📊 Expected unique employees (no dual): {expected_unique}")
        
        extraction_rate = (total_extracted / expected_unique) * 100 if expected_unique > 0 else 0
        print(f"   📊 Extraction completeness: {extraction_rate:.1f}%")
        
        # 5. IDENTIFY MISSING EMPLOYEES
        print(f"\n5. 📋 MISSING EMPLOYEE ANALYSIS:")
        
        missing_count = expected_unique - total_extracted
        if missing_count > 0:
            print(f"   ❌ Missing employees: {missing_count}")
            print(f"   📋 Possible reasons:")
            print(f"      - Extraction process incomplete")
            print(f"      - Some payslips failed to extract")
            print(f"      - Different PDF formats not recognized")
            print(f"      - Processing errors during extraction")
        else:
            print(f"   ✅ All expected employees extracted!")
        
        # 6. CHECK LATEST EXTRACTION SESSIONS
        print(f"\n6. 📋 LATEST EXTRACTION SESSION ANALYSIS:")
        
        # Get the latest extraction sessions
        cursor.execute("""
            SELECT session_id, 
                   COUNT(DISTINCT employee_id) as employees,
                   MAX(created_at) as latest_time
            FROM extracted_data
            GROUP BY session_id
            ORDER BY latest_time DESC
            LIMIT 3
        """)
        
        latest_sessions = cursor.fetchall()
        print(f"   📊 Latest extraction sessions:")
        
        for session_id, emp_count, latest_time in latest_sessions:
            print(f"      {session_id}: {emp_count} employees ({latest_time})")
            
            # Check period distribution in this session
            cursor.execute("""
                SELECT 
                    COUNT(CASE WHEN item_value LIKE '%07%' THEN 1 END) as july_records,
                    COUNT(CASE WHEN item_value LIKE '%06%' THEN 1 END) as june_records
                FROM extracted_data
                WHERE session_id = ?
            """, (session_id,))
            
            july_records, june_records = cursor.fetchone()
            print(f"         July records: {july_records}, June records: {june_records}")
        
        # 7. FINAL ASSESSMENT
        print(f"\n7. 🎯 FINAL ASSESSMENT:")
        print("=" * 50)
        
        if extraction_rate >= 95:
            print(f"   ✅ EXCELLENT: {extraction_rate:.1f}% extraction rate")
        elif extraction_rate >= 85:
            print(f"   ✅ GOOD: {extraction_rate:.1f}% extraction rate")
        elif extraction_rate >= 70:
            print(f"   ⚠️ PARTIAL: {extraction_rate:.1f}% extraction rate")
        else:
            print(f"   ❌ INCOMPLETE: {extraction_rate:.1f}% extraction rate")
        
        print(f"\n   📊 SUMMARY:")
        print(f"      Expected: July={expected_july}, June={expected_june}, Total={expected_total}")
        print(f"      Extracted: {total_extracted} unique employees")
        print(f"      Missing: {missing_count} employees")
        
        if missing_count > 0:
            print(f"\n   📋 RECOMMENDATION:")
            print(f"      Re-run extraction to capture the missing {missing_count} employees")
            print(f"      Check extraction logs for failed payslips")
            print(f"      Verify all PDF files were processed")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_payslip_counts()
    sys.exit(0 if success else 1)
