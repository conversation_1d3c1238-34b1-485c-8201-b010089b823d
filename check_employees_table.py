#!/usr/bin/env python3
"""
Check Employees Table
Check the employees table for the complete extraction data
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_employees_table():
    """Check the employees table for complete data"""
    print("🔍 CHECKING EMPLOYEES TABLE FOR COMPLETE DATA")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK LATEST EMPLOYEES TABLE SESSIONS
        print("1. 📋 LATEST EMPLOYEES TABLE SESSIONS:")
        
        cursor.execute("""
            SELECT session_id, 
                   COUNT(*) as total_employees,
                   COUNT(CASE WHEN department IS NOT NULL AND department != '' AND department != 'None' THEN 1 END) as with_dept,
                   MAX(created_at) as latest_time
            FROM employees
            GROUP BY session_id
            ORDER BY latest_time DESC
            LIMIT 10
        """)
        
        employee_sessions = cursor.fetchall()
        
        print(f"   📊 Latest employees table sessions:")
        for session_id, total, with_dept, latest_time in employee_sessions:
            status = "✅ COMPLETE" if total >= 5800 else "⚠️ PARTIAL" if total >= 2900 else "❌ INCOMPLETE"
            print(f"      {status} {session_id}")
            print(f"         Total: {total}, With Dept: {with_dept}, Time: {latest_time}")
        
        # Find the latest complete session
        latest_complete = None
        for session_id, total, with_dept, latest_time in employee_sessions:
            if total >= 5800:  # Complete
                latest_complete = session_id
                break
        
        if not latest_complete:
            print(f"\n   ⚠️ No complete sessions in employees table")
            # Use the most recent session
            latest_complete = employee_sessions[0][0] if employee_sessions else None
        
        if not latest_complete:
            print(f"   ❌ No sessions found in employees table")
            return False
        
        print(f"\n   🎯 Using employees session: {latest_complete}")
        
        # 2. CHECK THE 5 SPECIFIC EMPLOYEES IN EMPLOYEES TABLE
        print(f"\n2. 📋 CHECKING THE 5 EMPLOYEES IN EMPLOYEES TABLE:")
        
        target_employees = ['PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617']
        
        for emp_id in target_employees:
            print(f"\n   🔍 {emp_id}:")
            
            # Check in employees table
            cursor.execute("""
                SELECT employee_name, department, period_type
                FROM employees
                WHERE session_id = ? AND employee_id = ?
            """, (latest_complete, emp_id))
            
            emp_result = cursor.fetchone()
            
            if emp_result:
                name, dept, period = emp_result
                print(f"      ✅ EXISTS in employees table")
                print(f"      📋 NAME: {name}")
                print(f"      📋 DEPARTMENT: {dept if dept else 'NULL'}")
                print(f"      📋 PERIOD: {period}")
            else:
                print(f"      ❌ NOT FOUND in employees table session {latest_complete}")
                
                # Check if they exist in ANY employees session
                cursor.execute("""
                    SELECT session_id, employee_name, department
                    FROM employees
                    WHERE employee_id = ?
                    AND department IS NOT NULL AND department != '' AND department != 'None'
                    ORDER BY created_at DESC
                    LIMIT 3
                """, (emp_id,))
                
                other_sessions = cursor.fetchall()
                if other_sessions:
                    print(f"      📋 Found in other sessions:")
                    for session, name, dept in other_sessions:
                        print(f"         {session}: {name} - {dept}")
        
        # 3. CHECK COMPARISON DATA
        print(f"\n3. 📋 CHECKING COMPARISON DATA:")
        
        # Check if there are comparison tables
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE '%comparison%'
            ORDER BY name
        """)
        
        comparison_tables = cursor.fetchall()
        
        if comparison_tables:
            print(f"   📊 Comparison tables found:")
            for table in comparison_tables:
                table_name = table[0]
                print(f"      {table_name}")
                
                # Check if our employees are in comparison data
                try:
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table_name}
                        WHERE employee_id IN ('PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617')
                    """)
                    
                    count = cursor.fetchone()[0]
                    if count > 0:
                        print(f"         ✅ {count} of our target employees found")
                        
                        # Get details
                        cursor.execute(f"""
                            SELECT employee_id, employee_name, department
                            FROM {table_name}
                            WHERE employee_id IN ('PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617')
                        """)
                        
                        comp_results = cursor.fetchall()
                        for emp_id, name, dept in comp_results:
                            print(f"            {emp_id}: {name} - {dept}")
                    else:
                        print(f"         ❌ None of our target employees found")
                except Exception as e:
                    print(f"         ❌ Error checking table: {e}")
        else:
            print(f"   ❌ No comparison tables found")
        
        # 4. CHECK TRACKER RESULTS
        print(f"\n4. 📋 CHECKING TRACKER RESULTS:")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as items,
                   MAX(created_at) as latest_time
            FROM tracker_results
            GROUP BY session_id
            ORDER BY latest_time DESC
            LIMIT 5
        """)
        
        tracker_sessions = cursor.fetchall()
        
        if tracker_sessions:
            print(f"   📊 Tracker sessions:")
            for session_id, items, latest_time in tracker_sessions:
                print(f"      {session_id}: {items} items ({latest_time})")
        else:
            print(f"   ❌ No tracker results found")
        
        # 5. SUMMARY
        print(f"\n5. 🎯 DATA LOCATION SUMMARY:")
        print("=" * 50)
        
        print(f"📊 WHERE IS THE COMPLETE DATA?")
        
        # Check total counts across all sources
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) FROM extracted_data
        """)
        extracted_total = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) FROM employees
        """)
        employees_total = cursor.fetchone()[0]
        
        print(f"   📋 extracted_data table: {extracted_total} unique employees")
        print(f"   📋 employees table: {employees_total} unique employees")
        
        if employees_total >= 5800:
            print(f"   ✅ COMPLETE DATA is in EMPLOYEES TABLE")
        elif extracted_total >= 5800:
            print(f"   ✅ COMPLETE DATA is in EXTRACTED_DATA TABLE")
        else:
            print(f"   ❌ COMPLETE DATA not found in either table")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_employees_table()
    sys.exit(0 if success else 1)
