#!/usr/bin/env python3
"""
Find Latest Session
Find the latest extraction session with all 5919 employees
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def find_latest_session():
    """Find the latest session with complete extraction"""
    print("🔍 FINDING LATEST COMPLETE SESSION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK ALL RECENT SESSIONS
        print("1. 📋 RECENT EXTRACTION SESSIONS:")
        
        cursor.execute("""
            SELECT session_id, 
                   COUNT(DISTINCT employee_id) as employees,
                   <PERSON><PERSON>(created_at) as start_time,
                   MAX(created_at) as end_time
            FROM extracted_data
            GROUP BY session_id
            ORDER BY MAX(created_at) DESC
            LIMIT 10
        """)
        
        sessions = cursor.fetchall()
        
        latest_complete_session = None
        
        for session_id, employees, start_time, end_time in sessions:
            print(f"\n   📊 {session_id}:")
            print(f"      Employees: {employees}")
            print(f"      Time: {start_time} to {end_time}")
            
            if employees >= 5800:  # Close to 5919
                print(f"      ✅ COMPLETE SESSION (5919 expected)")
                if not latest_complete_session:
                    latest_complete_session = session_id
            elif employees >= 2900:
                print(f"      ⚠️ PARTIAL SESSION (~50% extracted)")
            else:
                print(f"      ❌ INCOMPLETE SESSION")
        
        # 2. CHECK TRACKER RESULTS TABLE
        print(f"\n2. 📋 TRACKER RESULTS SESSIONS:")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as tracker_items
            FROM tracker_results
            GROUP BY session_id
            ORDER BY session_id DESC
            LIMIT 5
        """)
        
        tracker_sessions = cursor.fetchall()
        
        for session_id, items in tracker_sessions:
            print(f"   📊 {session_id}: {items} tracker items")
        
        # 3. RECOMMEND ACTION
        print(f"\n3. 🎯 RECOMMENDATIONS:")
        print("=" * 50)
        
        if latest_complete_session:
            print(f"✅ LATEST COMPLETE SESSION FOUND: {latest_complete_session}")
            
            # Check if this session has department data
            cursor.execute("""
                SELECT COUNT(DISTINCT employee_id) as employees_with_dept
                FROM extracted_data
                WHERE session_id = ? 
                AND item_label = 'DEPARTMENT'
                AND item_value IS NOT NULL 
                AND item_value != ''
            """, (latest_complete_session,))
            
            dept_count = cursor.fetchone()[0]
            print(f"   📊 Employees with department data: {dept_count}")
            
            if dept_count >= 5800:
                print(f"   ✅ EXCELLENT: Complete session with department data")
                print(f"   📋 ACTION: Update tracker to use this session")
                
                # Check if we need to populate tracker for this session
                cursor.execute("""
                    SELECT COUNT(*) FROM tracker_results WHERE session_id = ?
                """, (latest_complete_session,))
                
                tracker_count = cursor.fetchone()[0]
                if tracker_count == 0:
                    print(f"   🚀 NEXT STEP: Populate tracker for session {latest_complete_session}")
                else:
                    print(f"   ✅ Tracker already has {tracker_count} items for this session")
            else:
                print(f"   ⚠️ WARNING: Session has employees but limited department data")
        else:
            print(f"❌ NO COMPLETE SESSION FOUND")
            print(f"   📋 Latest sessions only have ~2967 employees")
            print(f"   🚀 ACTION: Re-run extraction to get all 5919 employees")
        
        # 4. CHECK EMPLOYEES TABLE
        print(f"\n4. 📋 EMPLOYEES TABLE ANALYSIS:")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as employees,
                   COUNT(CASE WHEN department IS NOT NULL AND department != '' THEN 1 END) as with_dept
            FROM employees
            GROUP BY session_id
            ORDER BY COUNT(*) DESC
            LIMIT 5
        """)
        
        employee_sessions = cursor.fetchall()
        
        for session_id, employees, with_dept in employee_sessions:
            print(f"   📊 {session_id}: {employees} employees, {with_dept} with departments")
        
        conn.close()
        return latest_complete_session
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    session = find_latest_session()
    if session:
        print(f"\n🎯 LATEST COMPLETE SESSION: {session}")
    else:
        print(f"\n❌ NO COMPLETE SESSION FOUND")
    sys.exit(0)
