#!/usr/bin/env python3
"""
Perfect Section-Aware Extractor
The Perfect Extractor - Extracts raw data from ALL sections with 100% accuracy
"""

import fitz
import json
import re
from typing import Dict, List
from datetime import datetime

class PerfectSectionAwareExtractor:
    """The Perfect Extractor - Extract raw data from all sections with 100% accuracy"""

    def __init__(self, debug=True):
        self.debug = debug

        # Fallback section boundaries (used when dynamic detection fails)
        self.fallback_section_boundaries = {
            'PERSONAL DETAILS': {'y_min': 80, 'y_max': 140},
            'EARNINGS': {'y_min': 140, 'y_max': 300, 'x_min': 0, 'x_max': 240},
            'DEDUCTIONS': {'y_min': 140, 'y_max': 320, 'x_min': 240, 'x_max': 600},
            'LOANS': {'y_min': 320, 'y_max': 400},
            'EMPLOYERS CONTRIBUTION': {'y_min': 400, 'y_max': 450},
            'EMPLOYEE BANK DETAILS': {'y_min': 450, 'y_max': 500}
        }

        # Dynamic section boundaries (detected from actual payslip)
        self.section_boundaries = None

        # Load IN-HOUSE loan types for automatic EXTERNAL classification
        self.in_house_loan_types = set()
        self._load_in_house_loan_types()

        # REMOVED: No hardcoded section headers - use dynamic font-based detection
        # Main headings are detected by BOLD formatting and spatial positioning
        # Personal Details has NO main heading - detected by coordinate analysis

    def extract_perfect(self, pdf_path: str, page_num: int = 1) -> Dict:
        """ULTIMATE 100% DYNAMIC EXTRACTION - NO hardcoded patterns or business rules"""
        raw_data = self.extract_raw_data(pdf_path, page_num)

        # Extract simplified results for compatibility
        if 'error' in raw_data:
            return raw_data

        # Convert raw data to simple field-value pairs - PURE COORDINATE-BASED
        results = {}

        # Extract ALL label-value pairs from ALL sections dynamically
        for section_name, section_data in raw_data.get('sections', {}).items():
            pairs = section_data.get('extracted_pairs', [])

            for pair in pairs:
                label_text = pair['label']['text'].strip()
                value_text = pair['value']['text'].strip()

                # Skip obviously invalid pairings
                if self._is_invalid_pairing(label_text, value_text):
                    if self.debug:
                        print(f"   🚫 SKIPPED INVALID: '{label_text}' → '{value_text}' (from {section_name})")
                    continue

                # Normalize label names for consistency
                normalized_label = self._normalize_label_name(label_text)

                # Avoid duplicates - only store if not already present
                if normalized_label not in results:
                    results[normalized_label] = value_text
                    if self.debug:
                        print(f"   ✅ STORED: '{normalized_label}' → '{value_text}' (from {section_name})")
                else:
                    if self.debug:
                        print(f"   🔄 DUPLICATE SKIPPED: '{normalized_label}' → '{value_text}' (from {section_name}, already have: '{results[normalized_label]}')")

        return results

    def _is_invalid_pairing(self, label_text: str, value_text: str) -> bool:
        """Check if a label-value pairing is invalid and should be skipped"""

        # Filter out horizontal separators and decorative elements
        if self._is_horizontal_separator(label_text) or self._is_horizontal_separator(value_text):
            return True

        # Filter out empty or whitespace-only text
        if not label_text.strip() or not value_text.strip():
            return True

        # Filter out single characters or very short meaningless text
        if len(label_text.strip()) <= 1 or len(value_text.strip()) <= 1:
            return True

        # Strong label indicators
        label_indicators = ['NO.', 'NAME', 'ID', 'TITLE', 'EMPLOYEE', 'SSF', 'GHANA', 'SECTION', 'DEPARTMENT', 'JOB', 'BANK', 'ACCOUNT', 'BRANCH', 'SALARY', 'TAX', 'ALLOWANCE', 'DEDUCTION', 'FUND', 'WELFARE']

        # Check if value is actually a label (this is the root cause of duplicates)
        value_is_label = any(indicator in value_text.upper() for indicator in label_indicators)

        # Special exception for Bank name pairings - allow Bank -> Bank Name
        if (label_text.upper().strip() == 'BANK' and
            any(word in value_text.upper() for word in ['ECOBANK', 'ZENITH', 'LTD', 'LIMITED', 'BANK'])):
            return False  # Allow this pairing

        # Special exception for Employee Name pairings - allow Employee Name -> Person Name
        if ('NAME' in label_text.upper() and
            len(value_text.split()) >= 2 and
            value_text.replace(' ', '').replace('-', '').replace('.', '').isalpha() and
            len(value_text) >= 8):
            return False  # Allow this pairing

        # Invalid if value is clearly a label
        if value_is_label:
            return True

        # Invalid if both are the same text
        if label_text.upper().strip() == value_text.upper().strip():
            return True

        return False

    def _is_horizontal_separator(self, text: str) -> bool:
        """Check if text is a horizontal separator line"""
        text = text.strip()

        # Empty text
        if not text:
            return True

        # Check for separator patterns
        if len(text) >= 3:
            # Mostly underscores
            if text.count('_') >= len(text) * 0.8:
                return True
            # Mostly dashes
            if text.count('-') >= len(text) * 0.8:
                return True
            # Mostly equal signs
            if text.count('=') >= len(text) * 0.8:
                return True
            # Mostly dots
            if text.count('.') >= len(text) * 0.8:
                return True
            # Mostly spaces and underscores/dashes
            cleaned = text.replace(' ', '').replace('_', '').replace('-', '')
            if len(cleaned) <= 2:
                return True

        return False

    def _load_in_house_loan_types(self):
        """Load IN-HOUSE loan types from the dictionary for automatic EXTERNAL classification"""
        try:
            from core.dictionary_manager import PayrollDictionaryManager

            # Initialize dictionary manager
            dict_manager = PayrollDictionaryManager(debug=False)

            # Get all loan types classified as IN-HOUSE
            loan_types = dict_manager.get_loan_types()

            for loan_type_name, loan_data in loan_types.items():
                classification = loan_data.get('classification', '')
                if 'IN-HOUSE' in classification.upper():
                    self.in_house_loan_types.add(loan_type_name.upper())

            if self.debug:
                print(f"🏠 Loaded {len(self.in_house_loan_types)} IN-HOUSE loan types for automatic classification")
                for loan_type in sorted(self.in_house_loan_types):
                    print(f"   • {loan_type}")

        except Exception as e:
            if self.debug:
                print(f"⚠️  Warning: Could not load IN-HOUSE loan types: {e}")
            # Continue without automatic classification
            self.in_house_loan_types = set()

    def _classify_loan_automatically(self, loan_name: str) -> str:
        """
        Automatically classify a loan as IN-HOUSE or EXTERNAL based on dictionary.

        Args:
            loan_name: Name of the loan (e.g., "RENT ADVANCE", "STAFF LOAN")

        Returns:
            "IN-HOUSE LOAN" if found in dictionary, "EXTERNAL LOAN" otherwise
        """
        loan_name_upper = loan_name.upper().strip()

        # Check if this loan type is in our IN-HOUSE list
        for in_house_loan in self.in_house_loan_types:
            if in_house_loan in loan_name_upper or loan_name_upper in in_house_loan:
                if self.debug:
                    print(f"   🏠 CLASSIFIED: '{loan_name}' → IN-HOUSE LOAN (matches: {in_house_loan})")
                return "IN-HOUSE LOAN"

        # If not found in IN-HOUSE list, classify as EXTERNAL
        if self.debug:
            print(f"   🏦 CLASSIFIED: '{loan_name}' → EXTERNAL LOAN (not in IN-HOUSE list)")
        return "EXTERNAL LOAN"

    def _normalize_label_name(self, label_text: str) -> str:
        """Normalize label names for consistency"""
        label_upper = label_text.upper().strip()

        # Normalize common variations - BE VERY SPECIFIC to avoid conflicts
        if label_upper == 'EMPLOYEE NO.' or label_upper == 'EMPLOYEE NUMBER':
            return 'Employee No.'
        elif label_upper == 'EMPLOYEE NAME':
            return 'Employee Name'
        elif label_upper == 'SSF NO.' or label_upper == 'SSF NUMBER':
            return 'SSF No.'
        elif label_upper == 'GHANA CARD ID' or label_upper == 'GHANA CARD':
            return 'Ghana Card ID'
        elif label_upper == 'JOB TITLE':
            return 'Job Title'
        elif label_upper == 'ACCOUNT NO.' or label_upper == 'ACCOUNT NUMBER':
            return 'Account No.'
        else:
            return label_text  # Return original if no normalization needed

    def extract_raw_data(self, pdf_path: str, page_num: int = 1) -> Dict:
        """Extract raw data from all sections of a single payslip"""

        print(f"🔍 RAW DATA EXTRACTION: {pdf_path} Page {page_num}")
        print("=" * 80)

        try:
            # Extract all text elements
            all_elements = self._extract_all_text_elements(pdf_path, page_num)

            if not all_elements:
                print("❌ No text elements found")
                return {}

            print(f"📄 Found {len(all_elements)} total text elements")

            # Extract data from each section
            raw_data = {
                'file_info': {
                    'filename': pdf_path,
                    'page': page_num,
                    'extraction_time': datetime.now().isoformat(),
                    'total_elements': len(all_elements)
                },
                'sections': {}
            }

            # Detect dynamic section boundaries from actual payslip
            self.section_boundaries = self._detect_dynamic_section_boundaries(all_elements)

            # Store all elements for cross-section recovery
            self.all_elements_cache = all_elements

            if self.debug:
                print(f"🔍 Using {'DYNAMIC' if self.section_boundaries != self.fallback_section_boundaries else 'FALLBACK'} section boundaries")

            # Extract each section
            for section_name, boundaries in self.section_boundaries.items():
                section_data = self._extract_section_raw_data(all_elements, section_name, boundaries)
                raw_data['sections'][section_name] = section_data

            # Extract additional raw analysis
            raw_data['raw_analysis'] = self._perform_raw_analysis(all_elements)

            return raw_data

        except Exception as e:
            print(f"❌ Extraction error: {e}")
            return {'error': str(e)}

    def _extract_all_text_elements(self, pdf_path: str, page_num: int) -> List[Dict]:
        """Extract all text elements with coordinates"""

        all_elements = []

        doc = fitz.open(pdf_path)
        page = doc[page_num - 1]

        # Get text with coordinates
        text_dict = page.get_text("dict")

        for block in text_dict.get("blocks", []):
            if "lines" in block:
                for line in block["lines"]:
                    for span in line.get("spans", []):
                        text = span.get("text", "").strip()
                        if text:
                            bbox = span.get("bbox", [0, 0, 0, 0])
                            all_elements.append({
                                'text': text,
                                'x': round(bbox[0], 1),
                                'y': round(bbox[1], 1),
                                'width': round(bbox[2] - bbox[0], 1),
                                'height': round(bbox[3] - bbox[1], 1),
                                'font_size': round(span.get('size', 0), 1),
                                'font_flags': span.get('flags', 0)
                            })

        doc.close()
        return all_elements

    def _detect_dynamic_section_boundaries(self, all_elements: List[Dict]) -> Dict:
        """Detect section boundaries dynamically from actual payslip content"""

        if self.debug:
            print(f"\n🔍 DYNAMIC SECTION DETECTION:")
            print("-" * 50)

        # Find section headers in the document
        section_headers_found = {}

        # AUGMENTED: Dynamic main heading detection using font formatting and structure
        section_headers_found = self._detect_main_headings_by_formatting(all_elements)

        # AUGMENTED: Always use dynamic boundaries based on structural analysis
        if self.debug:
            print(f"   🎯 Found {len(section_headers_found)} main headings using font-based detection")
        return self._create_structure_aware_boundaries(section_headers_found, all_elements)

    def _create_dynamic_boundaries(self, section_headers: Dict, all_elements: List[Dict]) -> Dict:
        """Create precise dynamic section boundaries based on detected headers and content analysis"""

        if self.debug:
            print(f"\n📐 CREATING VISUAL STRUCTURE-BASED BOUNDARIES:")
            print("-" * 50)

        # Step 1: Detect horizontal separators (lines, underscores, etc.)
        horizontal_separators = self._detect_horizontal_separators(all_elements)

        # Step 2: Use main headings as section headers
        section_header_positions = list(section_headers.values())

        # Sort sections by Y position
        sorted_sections = sorted(section_headers.items(), key=lambda x: x[1]['y'])

        dynamic_boundaries = {}

        # Analyze document layout
        y_coords = [elem['y'] for elem in all_elements]
        x_coords = [elem['x'] for elem in all_elements]
        doc_y_min, doc_y_max = min(y_coords), max(y_coords)
        doc_x_min, doc_x_max = min(x_coords), max(x_coords)

        # Analyze X-coordinate distribution to find column boundaries
        column_analysis = self._analyze_column_structure(all_elements)

        if self.debug:
            print(f"   📏 Horizontal separators at Y: {[f'{y:.1f}' for y in horizontal_separators]}")
            header_info = [(h['text'], f"Y:{h['y']:.1f}") for h in section_header_positions]
            print(f"   📋 Section headers: {header_info}")
            print(f"   📊 Column boundaries: Left end: {column_analysis['left_column_end']:.1f}, Right start: {column_analysis['right_column_start']:.1f}")

        # Get precise header positions
        earnings_header_y = section_headers.get('EARNINGS', {}).get('y', doc_y_min + 100)
        deductions_header_y = section_headers.get('DEDUCTIONS', {}).get('y', doc_y_min + 100)
        main_headers_y = min(earnings_header_y, deductions_header_y)

        # Find the PRECISE end of personal details by analyzing content
        # Personal details must end BEFORE the EARNINGS/DEDUCTIONS headers
        personal_detail_end = main_headers_y - 20  # Conservative boundary before main headers

        # Ensure we don't include EARNINGS/DEDUCTIONS headers
        personal_detail_end = min(personal_detail_end, main_headers_y - 5)

        # PERSONAL DETAILS: Top section spanning FULL WIDTH (both left and right columns)
        # Personal details appear in both columns: Employee info on left, Section/Department/Job on right
        dynamic_boundaries['PERSONAL DETAILS'] = {
            'y_min': doc_y_min,
            'y_max': personal_detail_end,
            'x_min': doc_x_min,
            'x_max': doc_x_max  # Full width to capture Section, Department, Job Title on right side
        }

        # Find end of EARNINGS/DEDUCTIONS area using horizontal separators
        earnings_deductions_end = doc_y_max

        # Use horizontal separators to find section boundaries
        for separator_y in horizontal_separators:
            if separator_y > main_headers_y + 50:  # Must be well below main headers
                earnings_deductions_end = min(earnings_deductions_end, separator_y - 5)
                if self.debug:
                    print(f"   📏 Using horizontal separator at Y:{separator_y:.1f} as section boundary")
                break

        # Look for sections that come after EARNINGS/DEDUCTIONS
        later_sections = ['EMPLOYEE BANK DETAILS', 'EMPLOYERS CONTRIBUTION']
        for section in later_sections:
            if section in section_headers:
                section_y = section_headers[section]['y']
                if section_y > main_headers_y:
                    earnings_deductions_end = min(earnings_deductions_end, section_y - 10)
                    break

        # If no separator or later section found, estimate based on content
        if earnings_deductions_end == doc_y_max:
            # Look for "Employer's Contributions" or bank details patterns
            for elem in all_elements:
                text_upper = elem['text'].upper()
                if any(pattern in text_upper for pattern in ['EMPLOYER', 'BANK DETAILS', 'ACCOUNT']):
                    if elem['y'] > main_headers_y + 50:  # Must be well below headers
                        earnings_deductions_end = min(earnings_deductions_end, elem['y'] - 10)
                        break

        # EARNINGS: Left side of main content area with precise boundaries
        dynamic_boundaries['EARNINGS'] = {
            'y_min': personal_detail_end + 1,  # Start right after personal details
            'y_max': earnings_deductions_end,
            'x_min': doc_x_min,
            'x_max': column_analysis['left_column_end']
        }

        # DEDUCTIONS: Right side of main content area with precise boundaries
        dynamic_boundaries['DEDUCTIONS'] = {
            'y_min': personal_detail_end + 1,  # Start right after personal details
            'y_max': earnings_deductions_end,
            'x_min': column_analysis['right_column_start'],
            'x_max': doc_x_max
        }

        # Find LOANS section boundaries using horizontal separators
        loans_start = earnings_deductions_end
        loans_end = doc_y_max

        # Look for LOANS header and surrounding separators
        for header in section_header_positions:
            if 'LOAN' in header['text'].upper():
                loans_header_y = header['y']

                # Find separators around LOANS section
                for separator_y in horizontal_separators:
                    if separator_y < loans_header_y and separator_y > main_headers_y:
                        loans_start = separator_y + 5
                    elif separator_y > loans_header_y:
                        loans_end = separator_y - 5
                        break
                break

        # EMPLOYEE BANK DETAILS: Bottom section with enhanced detection
        bank_details_start = loans_end
        if 'EMPLOYEE BANK DETAILS' in section_headers:
            bank_y = section_headers['EMPLOYEE BANK DETAILS']['y']
            bank_details_start = min(bank_y - 10, bank_details_start)
        else:
            # Look for bank-related elements to determine start
            for elem in all_elements:
                if any(word in elem['text'].upper() for word in ['BANK', 'ACCOUNT', 'BRANCH']) and elem['y'] > loans_end:
                    bank_details_start = min(bank_details_start, elem['y'] - 5)
                    break

        dynamic_boundaries['EMPLOYEE BANK DETAILS'] = {
            'y_min': bank_details_start,
            'y_max': doc_y_max
        }

        # LOANS: Dedicated section with precise boundaries
        dynamic_boundaries['LOANS'] = {
            'y_min': loans_start,
            'y_max': loans_end,
            'x_min': doc_x_min,  # LOANS section spans full width
            'x_max': doc_x_max
        }

        # EMPLOYERS CONTRIBUTION: Between EARNINGS/DEDUCTIONS and LOANS
        dynamic_boundaries['EMPLOYERS CONTRIBUTION'] = {
            'y_min': earnings_deductions_end,
            'y_max': loans_start
        }

        # Debug output
        if self.debug:
            for section_name, boundaries in dynamic_boundaries.items():
                y_min, y_max = boundaries['y_min'], boundaries['y_max']
                x_info = ""
                if 'x_min' in boundaries:
                    x_info = f" X:{boundaries['x_min']:.1f}-{boundaries['x_max']:.1f}"
                print(f"   {section_name}: Y:{y_min:.1f}-{y_max:.1f}{x_info}")

        # Add missing sections with fallback boundaries
        for section_name in self.fallback_section_boundaries:
            if section_name not in dynamic_boundaries:
                dynamic_boundaries[section_name] = self.fallback_section_boundaries[section_name].copy()
                if self.debug:
                    print(f"   {section_name}: Using fallback boundaries (header not found)")

        return dynamic_boundaries

    def _detect_main_headings_by_formatting(self, all_elements: List[Dict]) -> Dict:
        """AUGMENTED: Detect main headings using font formatting and structural analysis"""

        if self.debug:
            print(f"\n🎯 AUGMENTED MAIN HEADING DETECTION:")
            print("-" * 50)

        main_headings = {}

        # Step 1: Analyze font characteristics to identify bold text
        font_analysis = self._analyze_font_characteristics(all_elements)

        # Step 2: Detect main headings by bold formatting and positioning
        for elem in all_elements:
            text = elem['text'].strip().upper()

            # Check if this element has bold characteristics
            is_bold = self._is_bold_text(elem, font_analysis)

            # Check if this matches main heading patterns (structure-based, not hardcoded)
            is_main_heading = self._is_structural_main_heading(elem, text, is_bold, all_elements)

            if is_main_heading:
                # Map to standardized section names
                section_name = self._map_to_section_name(text)
                if section_name:
                    main_headings[section_name] = {
                        'y': elem['y'],
                        'x': elem['x'],
                        'text': elem['text'],
                        'element': elem,
                        'is_bold': is_bold
                    }
                    if self.debug:
                        print(f"   📋 Main heading: {section_name} at Y:{elem['y']:.1f} X:{elem['x']:.1f} - '{elem['text']}' (Bold: {is_bold})")

        return main_headings

    def _analyze_font_characteristics(self, all_elements: List[Dict]) -> Dict:
        """Analyze font characteristics to identify bold text patterns"""

        font_sizes = [elem.get('font_size', 0) for elem in all_elements if elem.get('font_size', 0) > 0]
        font_flags = [elem.get('font_flags', 0) for elem in all_elements]

        # Calculate statistical thresholds
        avg_font_size = sum(font_sizes) / len(font_sizes) if font_sizes else 10
        max_font_size = max(font_sizes) if font_sizes else 12

        # Font flags analysis (bit 4 = bold in many PDF readers)
        bold_flag_threshold = 16  # Common bold flag value

        return {
            'avg_font_size': avg_font_size,
            'max_font_size': max_font_size,
            'bold_flag_threshold': bold_flag_threshold,
            'large_font_threshold': avg_font_size * 1.2
        }

    def _is_bold_text(self, elem: Dict, font_analysis: Dict) -> bool:
        """Determine if text element is bold using multiple indicators"""

        font_size = elem.get('font_size', 0)
        font_flags = elem.get('font_flags', 0)

        # Method 1: Font flags (most reliable)
        has_bold_flag = (font_flags & 16) != 0  # Bit 4 = bold

        # Method 2: Font size (larger than average)
        is_large_font = font_size > font_analysis['large_font_threshold']

        # Method 3: Font size relative to maximum
        is_prominent_size = font_size >= font_analysis['max_font_size'] * 0.9

        return has_bold_flag or is_large_font or is_prominent_size

    def _is_structural_main_heading(self, elem: Dict, text: str, is_bold: bool, all_elements: List[Dict]) -> bool:
        """Determine if element is a main heading based on structure, not hardcoded patterns"""

        # Structural characteristics of main headings:
        # 1. Bold formatting (preferred indicator)
        # 2. Short, descriptive text (1-3 words)
        # 3. Positioned to start new sections
        # 4. Not financial amounts or personal data

        # Basic structural requirements
        word_count = len(text.split())
        is_short_descriptive = 1 <= word_count <= 3
        is_not_financial = not self._is_likely_financial_amount(text)
        is_reasonable_length = 3 <= len(text) <= 30

        # Content analysis (structure-based, not hardcoded)
        has_section_characteristics = (
            any(word in text for word in ['EARNINGS', 'DEDUCTIONS', 'LOANS', 'BANK', 'DETAILS', 'CONTRIBUTION']) or
            (text == 'AMT (GHS)')  # Column header
        )

        # Position analysis - main headings often start new sections
        is_section_starter = self._is_section_starter_position(elem, all_elements)

        return (is_bold and is_short_descriptive and is_not_financial and
                is_reasonable_length and (has_section_characteristics or is_section_starter))

    def _is_section_starter_position(self, elem: Dict, all_elements: List[Dict]) -> bool:
        """Analyze if element is positioned like a section starter"""

        elem_y = elem['y']

        # Look for elements before and after to see if this starts a new content group
        elements_before = [e for e in all_elements if e['y'] < elem_y - 5]
        elements_after = [e for e in all_elements if e['y'] > elem_y + 5]

        # Section starters often have space before them and content after
        has_content_after = len(elements_after) > 3

        # Check if there's a gap before this element (indicating section break)
        if elements_before:
            closest_before = max(elements_before, key=lambda x: x['y'])
            gap_before = elem_y - closest_before['y']
            has_gap_before = gap_before > 10  # Significant gap
        else:
            has_gap_before = True  # At document start

        return has_content_after and has_gap_before

    def _map_to_section_name(self, text: str) -> str:
        """Map detected heading text to standardized section names"""

        # Structure-based mapping (not hardcoded patterns)
        text_clean = text.strip()

        if 'EARNING' in text_clean:
            return 'EARNINGS'
        elif 'DEDUCTION' in text_clean:
            return 'DEDUCTIONS'
        elif 'LOAN' in text_clean and len(text_clean) <= 10:  # Short LOAN text = main heading
            return 'LOANS'
        elif 'BANK' in text_clean and 'DETAIL' in text_clean:
            return 'EMPLOYEE BANK DETAILS'
        elif 'EMPLOYER' in text_clean and 'CONTRIBUTION' in text_clean:
            return 'EMPLOYERS CONTRIBUTION'
        elif text_clean == 'AMT (GHS)':
            return 'AMT_COLUMN'  # Special marker for amount columns

        return None  # Not a recognized main heading

    def _create_structure_aware_boundaries(self, main_headings: Dict, all_elements: List[Dict]) -> Dict:
        """AUGMENTED: Create boundaries using structural understanding of payslip layout"""

        if self.debug:
            print(f"\n📐 AUGMENTED STRUCTURE-AWARE BOUNDARIES:")
            print("-" * 50)

        # Step 1: Analyze document structure
        doc_analysis = self._analyze_document_structure(all_elements)

        # Step 2: Detect AMT (GHS) columns dynamically
        column_structure = self._detect_amt_columns(all_elements, main_headings)

        # Step 3: Find horizontal separators
        separators = self._detect_horizontal_separators(all_elements)

        # Step 4: Create boundaries based on structural understanding
        boundaries = {}

        # PERSONAL DETAILS: Top area with NO main heading
        personal_boundary = self._create_personal_details_boundary(all_elements, main_headings, doc_analysis)
        boundaries['PERSONAL DETAILS'] = personal_boundary

        # EARNINGS & DEDUCTIONS: Side-by-side with AMT columns
        earnings_boundary, deductions_boundary = self._create_earnings_deductions_boundaries(
            all_elements, main_headings, column_structure, personal_boundary, separators
        )
        boundaries['EARNINGS'] = earnings_boundary
        boundaries['DEDUCTIONS'] = deductions_boundary

        # LOANS: Vertical structure with column headers
        loans_boundary = self._create_loans_boundary(all_elements, main_headings, separators, deductions_boundary)
        boundaries['LOANS'] = loans_boundary

        # EMPLOYERS CONTRIBUTION: Between main content and loans
        contrib_boundary = self._create_contribution_boundary(all_elements, main_headings, earnings_boundary, loans_boundary)
        boundaries['EMPLOYERS CONTRIBUTION'] = contrib_boundary

        # EMPLOYEE BANK DETAILS: Bottom section
        bank_boundary = self._create_bank_details_boundary(all_elements, main_headings, loans_boundary, doc_analysis)
        boundaries['EMPLOYEE BANK DETAILS'] = bank_boundary

        if self.debug:
            for section, boundary in boundaries.items():
                y_range = f"Y:{boundary['y_min']:.1f}-{boundary['y_max']:.1f}"
                x_range = f" X:{boundary.get('x_min', 'full')}-{boundary.get('x_max', 'width')}" if 'x_min' in boundary else ""
                print(f"   {section}: {y_range}{x_range}")

        return boundaries

    def _analyze_document_structure(self, all_elements: List[Dict]) -> Dict:
        """Analyze overall document structure and layout"""

        y_coords = [elem['y'] for elem in all_elements]
        x_coords = [elem['x'] for elem in all_elements]

        return {
            'doc_y_min': min(y_coords),
            'doc_y_max': max(y_coords),
            'doc_x_min': min(x_coords),
            'doc_x_max': max(x_coords),
            'total_elements': len(all_elements)
        }

    def _detect_amt_columns(self, all_elements: List[Dict], main_headings: Dict) -> Dict:
        """AUGMENTED: Detect AMT (GHS) columns dynamically using structural analysis"""

        if self.debug:
            print(f"   💰 Detecting AMT (GHS) columns...")

        # Find AMT (GHS) headers
        amt_headers = []
        for elem in all_elements:
            if 'AMT' in elem['text'].upper() and 'GHS' in elem['text'].upper():
                amt_headers.append(elem)

        if self.debug:
            print(f"   Found {len(amt_headers)} AMT (GHS) headers")

        # Analyze X-coordinate clusters to find natural column divisions
        x_coords = [elem['x'] for elem in all_elements]
        x_clusters = self._find_position_clusters(x_coords, threshold=30)

        # Use EARNINGS and DEDUCTIONS main headings to determine column split
        earnings_x = main_headings.get('EARNINGS', {}).get('x', 0)
        deductions_x = main_headings.get('DEDUCTIONS', {}).get('x', 500)

        # Find the natural split point between EARNINGS and DEDUCTIONS
        split_point = (earnings_x + deductions_x) / 2

        # Refine split point using X-coordinate clusters
        if len(x_clusters) >= 2:
            sorted_clusters = sorted(x_clusters)

            # Find the cluster gap closest to our estimated split point
            best_split = split_point
            min_distance = float('inf')

            for i in range(len(sorted_clusters) - 1):
                gap_center = (sorted_clusters[i] + sorted_clusters[i + 1]) / 2
                distance = abs(gap_center - split_point)
                if distance < min_distance:
                    min_distance = distance
                    best_split = gap_center

            split_point = best_split

        return {
            'earnings_column_end': split_point - 10,
            'deductions_column_start': split_point + 10,
            'amt_headers': amt_headers,
            'split_point': split_point,
            'x_clusters': x_clusters
        }

    def _create_personal_details_boundary(self, all_elements: List[Dict], main_headings: Dict, doc_analysis: Dict) -> Dict:
        """Create boundary for Personal Details section (NO main heading)"""

        # Personal Details section starts AFTER the header area
        # Header contains: Period, Company name, logo - NOT personal details

        # Find where actual personal details start by looking for employee information
        personal_start_y = doc_analysis['doc_y_min']

        # Look for the first actual personal detail item (Employee No., Employee Name, etc.)
        personal_indicators = ['EMPLOYEE NO', 'EMPLOYEE NAME', 'SSF NO', 'GHANA CARD']

        for elem in all_elements:
            text_upper = elem['text'].upper()

            # Check if this is a personal detail label (not header content)
            is_personal_label = any(indicator in text_upper for indicator in personal_indicators)

            # Exclude header elements like "Period", company names, etc.
            is_header_element = (
                'PERIOD' in text_upper or
                'CHURCH' in text_upper or
                'PENTECOST' in text_upper or
                elem['y'] < doc_analysis['doc_y_min'] + 50  # Very top area is likely header
            )

            if is_personal_label and not is_header_element:
                personal_start_y = min(personal_start_y, elem['y'] - 5)
                break

        # If no personal indicators found, use a conservative start point
        if personal_start_y == doc_analysis['doc_y_min']:
            personal_start_y = doc_analysis['doc_y_min'] + 60  # Skip header area

        # End personal details well before the first main heading
        main_heading_y_positions = [h['y'] for h in main_headings.values()]

        if main_heading_y_positions:
            first_main_heading_y = min(main_heading_y_positions)
            personal_end = first_main_heading_y - 10
        else:
            # Fallback: use document structure analysis
            personal_end = personal_start_y + 100

        return {
            'y_min': personal_start_y,
            'y_max': personal_end,
            'x_min': doc_analysis['doc_x_min'],
            'x_max': doc_analysis['doc_x_max']  # Full width for personal details
        }

    def _create_earnings_deductions_boundaries(self, all_elements: List[Dict], main_headings: Dict,
                                             column_structure: Dict, personal_boundary: Dict, separators: List[float]) -> tuple:
        """Create boundaries for EARNINGS and DEDUCTIONS sections"""

        # Start after personal details
        start_y = personal_boundary['y_max'] + 1

        # Find end using separators or next section
        end_y = max([elem['y'] for elem in all_elements])  # Default to document end

        # Use separators to find section end - but include TOTAL DEDUCTIONS
        for sep_y in separators:
            if sep_y > start_y + 100:  # Must be well below start to include TOTAL DEDUCTIONS
                end_y = sep_y + 50  # Include larger area after separator for TOTAL DEDUCTIONS
                break

        # Check for sections that come after (LOANS, etc.)
        later_sections = ['LOANS', 'EMPLOYERS CONTRIBUTION', 'EMPLOYEE BANK DETAILS']
        for section in later_sections:
            if section in main_headings:
                section_y = main_headings[section]['y']
                if section_y > start_y:
                    end_y = min(end_y, section_y - 10)

        # EARNINGS: Left column INCLUDING its AMT column (first AMT column around X:177-184)
        earnings_boundary = {
            'y_min': start_y,
            'y_max': end_y,
            'x_min': personal_boundary['x_min'],
            'x_max': 220  # Fixed boundary to include first AMT column
        }

        # DEDUCTIONS: Right column starting after EARNINGS AMT column
        deductions_boundary = {
            'y_min': start_y,
            'y_max': end_y,
            'x_min': 225,  # Start after EARNINGS AMT column
            'x_max': personal_boundary['x_max']
        }

        return earnings_boundary, deductions_boundary

    def _create_loans_boundary(self, all_elements: List[Dict], main_headings: Dict, separators: List[float], deductions_boundary: Dict) -> Dict:
        """Create boundary for LOANS section with vertical structure"""

        # Find the actual LOANS heading to determine start
        loans_start_y = deductions_boundary['y_max'] + 1

        # Look for LOANS main heading
        if 'LOANS' in main_headings:
            loans_heading_y = main_headings['LOANS']['y']
            loans_start_y = max(loans_start_y, loans_heading_y - 10)

        # Find end using separators or next section
        end_y = max([elem['y'] for elem in all_elements])

        # Look for bank details section
        if 'EMPLOYEE BANK DETAILS' in main_headings:
            bank_y = main_headings['EMPLOYEE BANK DETAILS']['y']
            end_y = min(end_y, bank_y - 10)

        return {
            'y_min': loans_start_y,
            'y_max': end_y,
            'x_min': 36.8,  # Full width for loans - include all columns
            'x_max': 538.6
        }

    def _create_contribution_boundary(self, all_elements: List[Dict], main_headings: Dict, earnings_boundary: Dict, loans_boundary: Dict) -> Dict:
        """Create boundary for Employer's Contribution section"""

        # Find the actual Employer's Contribution heading if it exists
        if 'EMPLOYERS CONTRIBUTION' in main_headings:
            contrib_y = main_headings['EMPLOYERS CONTRIBUTION']['y']
            # Find elements in the contribution area
            contrib_elements = [elem for elem in all_elements
                              if contrib_y <= elem['y'] <= loans_boundary['y_min'] - 10]

            if contrib_elements:
                contrib_y_max = max(elem['y'] for elem in contrib_elements) + 10
            else:
                contrib_y_max = contrib_y + 50  # Default height

            return {
                'y_min': contrib_y - 5,
                'y_max': min(contrib_y_max, loans_boundary['y_min'] - 1),
                'x_min': earnings_boundary['x_min'],
                'x_max': 538.6  # Full width to include AMT columns
            }
        else:
            # No contribution section found - return empty boundary
            return {
                'y_min': earnings_boundary['y_max'] + 1,
                'y_max': earnings_boundary['y_max']  # Empty section
            }

    def _create_bank_details_boundary(self, all_elements: List[Dict], main_headings: Dict, loans_boundary: Dict, doc_analysis: Dict) -> Dict:
        """Create boundary for Employee Bank Details section"""

        # Start after loans or use main heading position
        start_y = loans_boundary['y_max'] + 1
        if 'EMPLOYEE BANK DETAILS' in main_headings:
            bank_y = main_headings['EMPLOYEE BANK DETAILS']['y']
            start_y = min(start_y, bank_y - 5)

        # Find the end boundary by excluding footer elements like "Printed on:"
        end_y = doc_analysis['doc_y_max']

        # Look for footer indicators that should NOT be part of bank details
        for elem in all_elements:
            text_upper = elem['text'].upper()
            elem_y = elem['y']

            # Check if this is a footer element (like "Printed on:", timestamps, software credits)
            is_footer_element = (
                'PRINTED' in text_upper or
                'AKATUA' in text_upper or  # Software credit
                'SOFTRIBE' in text_upper or
                # Time patterns (HH:MM:SS)
                (len(elem['text']) <= 10 and ':' in elem['text'] and
                 sum(1 for c in elem['text'] if c.isdigit()) >= 4) or
                # Date patterns at bottom
                (elem_y > start_y + 50 and '/' in elem['text'] and
                 sum(1 for c in elem['text'] if c.isdigit()) >= 4)
            )

            if is_footer_element and elem_y > start_y:
                # This footer element should not be included in bank details
                end_y = min(end_y, elem_y - 2)  # End just before footer

        return {
            'y_min': start_y,
            'y_max': end_y
        }

    def _analyze_column_structure(self, all_elements: List[Dict]) -> Dict:
        """LEGACY: Analyze X-coordinate distribution - REPLACED by _detect_amt_columns"""

        # This method is kept for compatibility but functionality moved to _detect_amt_columns
        # which provides better structural analysis

        x_coords = [elem['x'] for elem in all_elements]
        x_clusters = self._find_position_clusters(x_coords, threshold=30)

        # Simple fallback column detection
        if len(x_clusters) >= 2:
            sorted_clusters = sorted(x_clusters)
            split_point = (sorted_clusters[0] + sorted_clusters[-1]) / 2
            left_column_end = split_point - 10
            right_column_start = split_point + 10
        else:
            doc_x_min, doc_x_max = min(x_coords), max(x_coords)
            middle = (doc_x_min + doc_x_max) / 2
            left_column_end = middle - 20
            right_column_start = middle + 20

        return {
            'left_column_end': left_column_end,
            'right_column_start': right_column_start,
            'x_clusters': x_clusters
        }

    def _detect_horizontal_separators(self, elements: List[Dict]) -> List[float]:
        """Detect horizontal lines/separators that divide sections"""
        separators = []

        for elem in elements:
            text = elem['text'].strip()
            # Look for horizontal line patterns
            if (len(text) > 10 and
                (text.count('_') > len(text) * 0.8 or  # Underscores
                 text.count('-') > len(text) * 0.8 or  # Dashes
                 text.count('=') > len(text) * 0.8)):  # Equal signs
                separators.append(elem['y'])
                if self.debug:
                    print(f"   📏 Found horizontal separator at Y:{elem['y']:.1f}: '{text[:20]}...'")

        # Remove duplicates and sort
        separators = sorted(list(set(separators)))
        return separators

    def _find_position_clusters(self, coordinates: List[float], threshold: float = 30) -> List[float]:
        """Find clusters of coordinates to identify column/row boundaries"""

        if not coordinates:
            return []

        # Sort coordinates
        sorted_coords = sorted(set(coordinates))

        if len(sorted_coords) <= 1:
            return sorted_coords

        # Find clusters using threshold
        clusters = []
        current_cluster = [sorted_coords[0]]

        for i in range(1, len(sorted_coords)):
            if sorted_coords[i] - sorted_coords[i-1] <= threshold:
                # Add to current cluster
                current_cluster.append(sorted_coords[i])
            else:
                # Start new cluster
                # Use average of current cluster as representative
                cluster_center = sum(current_cluster) / len(current_cluster)
                clusters.append(cluster_center)
                current_cluster = [sorted_coords[i]]

        # Add the last cluster
        if current_cluster:
            cluster_center = sum(current_cluster) / len(current_cluster)
            clusters.append(cluster_center)

        return clusters

    # REMOVED: Old hardcoded section header detection
    # Now using font-based main heading detection in _detect_main_headings_by_formatting

    # ALL VALIDATION METHODS REMOVED - PURE COORDINATE-BASED EXTRACTION

    def _extract_section_raw_data(self, all_elements: List[Dict], section_name: str, boundaries: Dict) -> Dict:
        """Extract raw data from a specific section with enhanced classification"""

        if self.debug:
            print(f"\n📋 {section_name} SECTION:")
            print("-" * 50)

        # Filter elements by boundaries
        section_elements = []
        misclassified_elements = []  # Track potentially misclassified items

        for elem in all_elements:
            x, y = elem['x'], elem['y']

            # Check Y boundaries
            if boundaries['y_min'] <= y <= boundaries['y_max']:
                # Check X boundaries if specified
                if 'x_min' in boundaries and 'x_max' in boundaries:
                    if boundaries['x_min'] <= x <= boundaries['x_max']:
                        section_elements.append(elem)
                    else:
                        # Element is in Y range but wrong X range - potential misclassification
                        misclassified_elements.append(elem)
                else:
                    section_elements.append(elem)

        # PURE COORDINATE-BASED EXTRACTION: No business rules, no hardcoded validation
        # Trust the section boundaries completely - let auto-learning handle everything

        # Sort elements by position (Y first, then X)
        section_elements.sort(key=lambda x: (x['y'], x['x']))

        section_data = {
            'element_count': len(section_elements),
            'boundaries': boundaries,
            'raw_elements': section_elements,
            'extracted_pairs': [],
            'potential_labels': [],
            'potential_values': [],
            'financial_amounts': [],
            'patterns_found': {}
        }

        if self.debug:
            print(f"   Found {len(section_elements)} elements")

        # Analyze elements
        for elem in section_elements:
            text = elem['text']

            if self.debug:
                print(f"   Y:{elem['y']:6.1f} X:{elem['x']:6.1f} | {text}")

            # Identify different types of elements
            if self._is_potential_label(text):
                section_data['potential_labels'].append(elem)

            if self._is_likely_financial_amount(text):
                section_data['financial_amounts'].append(elem)
                section_data['potential_values'].append(elem)
            elif self._is_potential_value(text):
                section_data['potential_values'].append(elem)

        # Find label-value pairs - SPECIAL HANDLING FOR LOANS TABULAR STRUCTURE
        if section_name == 'LOANS':
            # Use specialized tabular extraction for LOANS section
            section_data['extracted_pairs'] = self._extract_loans_tabular_data(section_elements)
        else:
            # Use standard label-value pairing for all other sections
            section_data['extracted_pairs'] = self._find_label_value_pairs(section_elements, section_name)

        # Find patterns
        section_data['patterns_found'] = self._find_section_patterns(section_elements, section_name)

        return section_data

    def _is_potential_label(self, text: str) -> bool:
        """ULTIMATE DYNAMIC LABEL DETECTION - Pure statistical analysis only"""

        # Basic text validation
        if not text or len(text.strip()) < 2:
            return False

        # Filter out horizontal separators first
        if self._is_horizontal_separator(text):
            return False

        # Statistical analysis only
        char_stats = self._analyze_text_characteristics(text)

        # Simple statistical criteria - NO hardcoded business rules
        has_letters = char_stats['letter_count'] > 0
        reasonable_length = 2 <= len(text) <= 100
        not_purely_numeric = char_stats['letter_ratio'] > 0.1
        not_purely_financial = not self._is_likely_financial_amount(text)

        return has_letters and reasonable_length and not_purely_numeric and not_purely_financial

    def _is_likely_financial_amount(self, text: str) -> bool:
        """Check if text is likely a financial amount using statistical analysis"""

        # Remove common separators for analysis
        clean_text = text.replace(',', '').replace(' ', '')

        # Statistical characteristics of financial amounts
        char_stats = self._analyze_text_characteristics(clean_text)

        # Financial amounts typically have:
        # 1. High digit ratio
        # 2. Limited special characters (mainly . and -)
        # 3. Reasonable length for monetary values
        # 4. Decimal structure (but not always)

        has_high_digit_ratio = char_stats['digit_ratio'] > 0.7
        reasonable_length = 1 <= len(clean_text) <= 15
        limited_special_chars = char_stats['symbol_count'] <= 2
        no_letters = char_stats['letter_count'] == 0

        # Check for decimal-like structure (optional)
        has_decimal_structure = '.' in text and text.count('.') == 1

        # Basic financial amount characteristics
        basic_financial = (has_high_digit_ratio and reasonable_length and
                          limited_special_chars and no_letters)

        return basic_financial

    def _is_potential_value(self, text: str) -> bool:
        """ULTIMATE DYNAMIC VALUE DETECTION - Pure statistical analysis only"""

        # Basic text validation
        if not text or len(text.strip()) < 1:
            return False

        # Filter out horizontal separators first
        if self._is_horizontal_separator(text):
            return False

        # Simple statistical criteria - NO hardcoded business rules
        has_content = len(text.strip()) > 0
        reasonable_length = 1 <= len(text) <= 200

        # Accept anything that could be meaningful data
        return has_content and reasonable_length

    def _analyze_text_characteristics(self, text: str) -> Dict:
        """Analyze statistical characteristics of text without hardcoded patterns"""

        if not text:
            return {
                'length': 0, 'letter_count': 0, 'digit_count': 0, 'symbol_count': 0,
                'letter_ratio': 0, 'digit_ratio': 0, 'symbol_ratio': 0,
                'word_count': 0, 'has_spaces': False
            }

        # Count character types
        letter_count = sum(1 for c in text if c.isalpha())
        digit_count = sum(1 for c in text if c.isdigit())
        space_count = sum(1 for c in text if c.isspace())
        symbol_count = len(text) - letter_count - digit_count - space_count

        total_chars = len(text)

        # Calculate ratios
        letter_ratio = letter_count / total_chars if total_chars > 0 else 0
        digit_ratio = digit_count / total_chars if total_chars > 0 else 0
        symbol_ratio = symbol_count / total_chars if total_chars > 0 else 0

        # Word analysis
        words = text.split()
        word_count = len(words)
        has_spaces = ' ' in text

        return {
            'length': total_chars,
            'letter_count': letter_count,
            'digit_count': digit_count,
            'symbol_count': symbol_count,
            'letter_ratio': letter_ratio,
            'digit_ratio': digit_ratio,
            'symbol_ratio': symbol_ratio,
            'word_count': word_count,
            'has_spaces': has_spaces
        }

    def _find_label_value_pairs(self, elements: List[Dict], section_name: str = '') -> List[Dict]:
        """ULTIMATE 100% ACCURATE PAIRING - Smart coordinate-based with proper label-value detection"""

        if self.debug:
            print(f"   🔍 ULTIMATE ACCURATE PAIRING: Analyzing {len(elements)} elements")

        pairs = []
        used_elements = set()

        # Sort elements by Y position, then X position for systematic processing
        sorted_elements = sorted(elements, key=lambda x: (x['y'], x['x']))

        # STEP 1: Find ALL possible pairs and rank by quality
        all_possible_pairs = []

        for i, elem in enumerate(sorted_elements):
            if self._is_section_header_or_decoration(elem['text'].strip()):
                continue

            for j, candidate in enumerate(sorted_elements):
                if i == j or self._is_section_header_or_decoration(candidate['text'].strip()):
                    continue

                elem_text = elem['text'].strip()
                candidate_text = candidate['text'].strip()

                # Skip if both are obvious labels
                if self._are_both_obvious_labels(elem_text, candidate_text):
                    continue

                # Calculate distances
                x_distance = abs(candidate['x'] - elem['x'])
                y_distance = abs(candidate['y'] - elem['y'])
                total_distance = x_distance + y_distance

                # Check if this is a valid pairing - ENHANCED FOR AMT COLUMNS
                is_same_row = y_distance <= 8 and 5 <= x_distance <= 600  # Even more flexible for AMT columns
                is_nearby_row = 5 <= y_distance <= 30 and x_distance <= 300  # More flexible for cross-column pairing

                # SPECIAL CASE: Employee Name pairing - allow small Y-coordinate differences in Personal Details
                if (section_name == 'PERSONAL DETAILS' and
                    y_distance <= 3 and x_distance <= 100 and
                    ('NAME' in elem['text'].upper() or 'NAME' in candidate['text'].upper())):
                    is_same_row = True  # Force same-row treatment for Employee Name pairings

                # For same-row pairings, prefer closer X coordinates to avoid wrong pairings
                if is_same_row and y_distance <= 3:  # Very same row
                    # Penalize if there's a closer value in the same row
                    closer_values = [v for v in sorted_elements
                                   if v != candidate and
                                   abs(v['y'] - elem['y']) <= 3 and
                                   v['x'] > elem['x'] and
                                   abs(v['x'] - elem['x']) < x_distance and
                                   self._is_likely_financial_amount(v['text'])]
                    if closer_values:
                        continue  # Skip this pairing, there's a closer value

                # Special handling for LOANS section - allow vertical pairing
                if section_name == 'LOANS' and not is_same_row:
                    # In loans section, allow more flexible vertical pairing
                    if y_distance <= 15 and x_distance <= 100:  # More flexible for loans
                        is_nearby_row = True

                if is_same_row or is_nearby_row:
                    # Determine label-value orientation
                    label_elem, value_elem = self._determine_label_value_orientation(elem, candidate)

                    # Skip if invalid pairing (both are labels)
                    if label_elem is None or value_elem is None:
                        continue

                    # Calculate quality score based on proximity and label-value fit
                    quality_score = self._calculate_pairing_quality(label_elem, value_elem, total_distance, is_same_row)

                    all_possible_pairs.append({
                        'label': label_elem,
                        'value': value_elem,
                        'distance': total_distance,
                        'quality_score': quality_score,
                        'is_same_row': is_same_row
                    })

        # Sort by quality score (highest first)
        all_possible_pairs.sort(key=lambda x: x['quality_score'], reverse=True)

        # Select non-conflicting pairs greedily, but only high-quality ones
        for pair in all_possible_pairs:
            label_id = id(pair['label'])
            value_id = id(pair['value'])

            # Skip if either element is already used
            if label_id in used_elements or value_id in used_elements:
                continue

            # Only accept pairs with positive quality scores
            if pair['quality_score'] <= 0:
                continue

            # Add this pair
            pairs.append({
                'label': pair['label'],
                'value': pair['value'],
                'pairing_type': 'same_row' if pair['is_same_row'] else 'nearby_row',
                'distance': pair['distance'],
                'confidence': 1.0
            })

            used_elements.add(label_id)
            used_elements.add(value_id)

            if self.debug:
                pair_type = 'same_row' if pair['is_same_row'] else 'nearby_row'
                print(f"   ✅ PAIRED: '{pair['label']['text']}' → '{pair['value']['text']}' ({pair_type}, quality: {pair['quality_score']:.2f})")

        # Debug: Show unpaired elements that might include Department or Bank
        if self.debug:
            unpaired_elements = [elem for elem in sorted_elements
                               if id(elem) not in used_elements and
                               not self._is_section_header_or_decoration(elem['text'].strip())]
            if unpaired_elements:
                print(f"   🔍 UNPAIRED ELEMENTS ({len(unpaired_elements)}):")
                for elem in unpaired_elements:
                    print(f"      • '{elem['text']}' at Y:{elem['y']:.1f} X:{elem['x']:.1f}")
                    if 'DEPARTMENT' in elem['text'].upper():
                        print(f"        ⚠️  DEPARTMENT ELEMENT FOUND BUT NOT PAIRED!")
                    if 'BANK' in elem['text'].upper():
                        print(f"        ⚠️  BANK ELEMENT FOUND BUT NOT PAIRED!")

        # FORCE EMPLOYEE NAME PAIRING if both Employee Name and a name exist unpaired in Personal Details
        if section_name == 'PERSONAL DETAILS':
            employee_name_label = None
            employee_name_value = None
            for elem in sorted_elements:
                if id(elem) not in used_elements:
                    if 'NAME' in elem['text'].upper():
                        employee_name_label = elem
                    elif (len(elem['text'].split()) >= 2 and
                          elem['text'].replace(' ', '').replace('-', '').replace('.', '').isalpha() and
                          len(elem['text']) >= 8):
                        employee_name_value = elem

            if employee_name_label and employee_name_value:
                if self.debug:
                    print(f"   🔧 FORCING EMPLOYEE NAME PAIRING: '{employee_name_label['text']}' → '{employee_name_value['text']}'")
                pairs.append({
                    'label': employee_name_label,
                    'value': employee_name_value,
                    'pairing_type': 'forced_name_pairing',
                    'distance': abs(employee_name_label['x'] - employee_name_value['x']) + abs(employee_name_label['y'] - employee_name_value['y']),
                    'confidence': 1.0
                })
                used_elements.add(id(employee_name_label))
                used_elements.add(id(employee_name_value))

        # FORCE BANK PAIRING if both Bank and bank name exist unpaired
        if section_name == 'EMPLOYEE BANK DETAILS':
            bank_label = None
            bank_value = None
            account_label = None
            account_value = None
            branch_label = None
            branch_value = None

            for elem in sorted_elements:
                if id(elem) not in used_elements:
                    elem_text = elem['text'].strip().upper()

                    # Enhanced Bank label detection
                    if 'BANK' in elem_text and len(elem_text) <= 15 and not self._is_likely_financial_amount(elem['text']):
                        bank_label = elem

                    # Enhanced Bank value detection (any bank name)
                    elif (any(word in elem_text for word in ['BANK', 'LTD', 'LIMITED', 'ZENITH', 'ECOBANK', 'GCB', 'UBA', 'STANBIC', 'FIDELITY', 'CAL', 'REPUBLIC']) and
                          len(elem_text) > 3 and not self._is_likely_financial_amount(elem['text'])):
                        bank_value = elem

                    # Account No. label detection
                    elif ('ACCOUNT' in elem_text and 'NO' in elem_text) or elem_text == 'ACCOUNT NO.':
                        account_label = elem

                    # Account number value detection (long numeric sequences)
                    elif (elem['text'].replace(' ', '').replace('-', '').isdigit() and
                          len(elem['text'].replace(' ', '').replace('-', '')) >= 10):
                        account_value = elem

                    # Branch label detection
                    elif 'BRANCH' in elem_text and len(elem_text) <= 15:
                        branch_label = elem

                    # Branch value detection (text that could be a branch name)
                    elif (len(elem_text.split()) >= 1 and elem['text'].replace(' ', '').replace('-', '').isalpha() and
                          len(elem_text) > 3 and len(elem_text) < 50 and
                          not any(word in elem_text for word in ['EMPLOYEE', 'BANK', 'ACCOUNT', 'BRANCH', 'NAME', 'NO.'])):
                        if not branch_value:  # Take the first suitable branch value
                            branch_value = elem

            # Force Bank pairing
            if bank_label and bank_value:
                if self.debug:
                    print(f"   🔧 FORCING BANK PAIRING: '{bank_label['text']}' → '{bank_value['text']}'")
                pairs.append({
                    'label': bank_label,
                    'value': bank_value,
                    'pairing_type': 'forced_bank_pairing',
                    'distance': abs(bank_label['x'] - bank_value['x']) + abs(bank_label['y'] - bank_value['y']),
                    'confidence': 1.0
                })
                used_elements.add(id(bank_label))
                used_elements.add(id(bank_value))

            # Force Account No. pairing
            if account_label and account_value:
                if self.debug:
                    print(f"   🔧 FORCING ACCOUNT PAIRING: '{account_label['text']}' → '{account_value['text']}'")
                pairs.append({
                    'label': account_label,
                    'value': account_value,
                    'pairing_type': 'forced_account_pairing',
                    'distance': abs(account_label['x'] - account_value['x']) + abs(account_label['y'] - account_value['y']),
                    'confidence': 1.0
                })
                used_elements.add(id(account_label))
                used_elements.add(id(account_value))

            # Force Branch pairing
            if branch_label and branch_value:
                if self.debug:
                    print(f"   🔧 FORCING BRANCH PAIRING: '{branch_label['text']}' → '{branch_value['text']}'")
                pairs.append({
                    'label': branch_label,
                    'value': branch_value,
                    'pairing_type': 'forced_branch_pairing',
                    'distance': abs(branch_label['x'] - branch_value['x']) + abs(branch_label['y'] - branch_value['y']),
                    'confidence': 1.0
                })
                used_elements.add(id(branch_label))
                used_elements.add(id(branch_value))



        if self.debug:
            print(f"   📊 Final pairs: {len(pairs)} accurate coordinate-based pairs")

        return pairs

    def _calculate_pairing_quality(self, label_elem: Dict, value_elem: Dict, distance: float, is_same_row: bool) -> float:
        """Calculate quality score for a label-value pairing"""

        label_text = label_elem['text'].strip()
        value_text = value_elem['text'].strip()

        # Base score
        score = 1.0

        # Distance penalty (closer is better)
        distance_penalty = distance / 100.0  # Normalize distance
        score -= min(distance_penalty, 0.5)  # Cap penalty at 0.5

        # Same row bonus (stronger preference) with X-coordinate preference
        if is_same_row:
            score += 0.5  # Increased bonus for same-row pairings
            # Extra bonus for closer X coordinates in same row to prevent value swapping
            x_distance = abs(value_elem['x'] - label_elem['x'])
            if x_distance < 100:
                score += 1.5  # Strong bonus for very close X coordinates
            elif x_distance < 200:
                score += 0.8  # Moderate bonus for moderately close
            elif x_distance < 300:
                score += 0.3  # Small bonus for reasonably close
            else:
                score -= 0.2  # Small penalty for distant pairings

        # Label quality bonus
        label_indicators = ['NO.', 'NAME', 'ID', 'TITLE', 'EMPLOYEE', 'SSF', 'GHANA', 'SECTION', 'DEPARTMENT', 'JOB', 'BANK', 'ACCOUNT', 'BRANCH']
        if any(indicator in label_text.upper() for indicator in label_indicators):
            score += 0.4

        # Value quality bonus (ENHANCED for department names with special characters)
        if (value_text.upper().startswith(('COP', 'SEC', 'E', 'PW', 'GHA-')) or
            self._is_likely_financial_amount(value_text) or
            (len(value_text.split()) >= 2 and self._is_mostly_alphabetic(value_text)) or
            any(word in value_text.upper() for word in ['AREA', 'WID', 'PENSIONS', 'KASOA', 'NSAWAM', 'MINISTERS', 'STAFF'])):
            score += 0.3

        # Penalty for obvious mismatches
        if label_text.upper() == value_text.upper():
            score -= 1.0

        # MASSIVE bonus for correct semantic pairings
        if ('NAME' in label_text.upper() and
            len(value_text.split()) >= 2 and
            value_text.replace(' ', '').isalpha()):
            score += 2.0  # Huge bonus for name pairings

        # Enhanced bonus for Bank -> Bank Name pairings (OVERRIDE LABEL-LABEL PENALTY)
        if ('BANK' in label_text.upper() and len(label_text) <= 15 and
            any(word in value_text.upper() for word in ['BANK', 'LTD', 'LIMITED', 'ECOBANK', 'ZENITH', 'GCB', 'UBA', 'STANBIC', 'FIDELITY', 'CAL', 'REPUBLIC'])):
            score += 15.0  # MASSIVE bonus to override all penalties

        # Enhanced bonus for Account No. -> Account Number pairings
        if ('ACCOUNT' in label_text.upper() and 'NO' in label_text.upper() and
            value_text.replace(' ', '').replace('-', '').isdigit() and len(value_text.replace(' ', '').replace('-', '')) >= 10):
            score += 5.0  # MASSIVE bonus for account number pairings

        # Enhanced bonus for Branch -> Branch Name pairings
        if ('BRANCH' in label_text.upper() and len(label_text) <= 15 and
            len(value_text.split()) >= 1 and value_text.replace(' ', '').replace('-', '').isalpha() and
            len(value_text) > 3 and len(value_text) < 50 and
            not any(word in value_text.upper() for word in ['EMPLOYEE', 'BANK', 'ACCOUNT', 'BRANCH', 'NAME', 'NO.'])):
            score += 3.0  # Strong bonus for branch name pairings

        # Special bonus for Ghana Card ID -> GHA-... pairings
        if ('GHANA' in label_text.upper() and 'ID' in label_text.upper() and
            value_text.upper().startswith('GHA-')):
            score += 2.0  # Huge bonus for Ghana Card pairings

        # TARGETED FIX: TAXABLE SALARY should pair with larger amounts (typically over 5000)
        if ('TAXABLE' in label_text.upper() and 'SALARY' in label_text.upper() and
            self._is_likely_financial_amount(value_text)):
            try:
                amount = float(value_text.replace(',', ''))
                if amount > 5000:  # TAXABLE SALARY is typically a large amount
                    score += 3.0  # Strong bonus for pairing with large amounts
                elif amount > 2000:  # Moderate range
                    score += 1.0  # Smaller bonus for moderate amounts
                else:
                    score -= 2.0  # Strong penalty for pairing with small amounts (likely SSF EMPLOYEE)
            except:
                pass

        # TARGETED FIX: SSF EMPLOYEE/EEMPLOYEE should pair with smaller amounts (typically under 1000)
        if ('SSF' in label_text.upper() and ('EMPLOYEE' in label_text.upper() or 'EEMPLOYEE' in label_text.upper()) and
            self._is_likely_financial_amount(value_text)):
            try:
                amount = float(value_text.replace(',', ''))
                if amount < 1000:  # SSF EMPLOYEE is typically a smaller amount
                    score += 3.0  # Strong bonus for pairing with small amounts
                elif amount < 2000:  # Moderate range
                    score += 1.0  # Smaller bonus for moderate amounts
                else:
                    score -= 2.0  # Strong penalty for pairing with large amounts (likely TAXABLE SALARY)
            except:
                pass

        # ANTI-SWAPPING MECHANISM: Prevent TAXABLE SALARY and SSF EMPLOYEE value swapping
        if (('TAXABLE' in label_text.upper() and 'SALARY' in label_text.upper()) and
            ('SSF' in value_text.upper() or 'EMPLOYEE' in value_text.upper())):
            score -= 10.0  # Massive penalty for TAXABLE SALARY paired with SSF-related text

        if (('SSF' in label_text.upper() and ('EMPLOYEE' in label_text.upper() or 'EEMPLOYEE' in label_text.upper())) and
            ('TAXABLE' in value_text.upper() or 'SALARY' in value_text.upper())):
            score -= 10.0  # Massive penalty for SSF EMPLOYEE/EEMPLOYEE paired with TAXABLE-related text

        # MASSIVE penalty for earnings labels paired with other earnings labels
        earnings_labels = ['ALLOWANCE', 'SALARY', 'ELEMENT', 'FUEL', 'LEAVE', 'VEHICLE', 'MAINT', 'PROFESSIONAL', 'HOUSING', 'HEAD', 'DEPT', 'SECURITY', 'GUARD']
        label_is_earnings = any(indicator in label_text.upper() for indicator in earnings_labels)
        value_is_earnings = any(indicator in value_text.upper() for indicator in earnings_labels)

        if label_is_earnings and value_is_earnings:
            score -= 10.0  # MASSIVE penalty for pairing two earnings labels

        # MASSIVE penalty for deduction labels paired with other deduction labels
        deduction_labels = ['DEDUCTION', 'WELFARE', 'FUND', 'UNION', 'HEALTH', 'CREDIT', 'MUTUAL', 'LIFE', 'VANGUARD', 'STAFF', 'LOAN', 'BALANCE', 'CURRENT', 'OUST']
        label_is_deduction = any(indicator in label_text.upper() for indicator in deduction_labels)
        value_is_deduction = any(indicator in value_text.upper() for indicator in deduction_labels)

        if label_is_deduction and value_is_deduction:
            score -= 10.0  # MASSIVE penalty for pairing two deduction labels

        # HUGE bonus for pairing labels with financial amounts
        if (any(indicator in label_text.upper() for indicator in ['SALARY', 'ALLOWANCE', 'TAX', 'FUND', 'WELFARE', 'DEDUCTION']) and
            self._is_likely_financial_amount(value_text)):
            score += 3.0  # Huge bonus for label-amount pairings

            # ADDITIONAL VALIDATION: Ensure amount ranges match expected field types
            try:
                amount = float(value_text.replace(',', ''))
                # TAXABLE SALARY should be larger than SSF EMPLOYEE
                if 'TAXABLE' in label_text.upper() and 'SALARY' in label_text.upper():
                    if amount < 1000:  # Too small for TAXABLE SALARY
                        score -= 5.0  # Strong penalty
                elif 'SSF' in label_text.upper() and ('EMPLOYEE' in label_text.upper() or 'EEMPLOYEE' in label_text.upper()):
                    if amount > 5000:  # Too large for SSF EMPLOYEE/EEMPLOYEE
                        score -= 5.0  # Strong penalty
            except:
                pass

        # MASSIVE penalty for semantic mismatches (but allow STAFF, RESOURCE, HUMAN for departments)
        # FIXED: Allow department names with hyphens, slashes, and special characters
        if ('DEPARTMENT' in label_text.upper() and
            len(value_text.split()) >= 2 and
            self._is_mostly_alphabetic(value_text) and  # Fixed: Use custom function instead of isalpha()
            not any(word in value_text.upper() for word in ['CENTER', 'DEPARTMENT', 'MINISTRY', 'OFFICE', 'STAFF', 'RESOURCE', 'HUMAN', 'HEADQUARTERS', 'AREA', 'WID', 'PENSIONS', 'KASOA', 'NSAWAM'])):
            score -= 2.0  # Huge penalty for department paired with personal names

        # Special bonus for Department -> Department Name pairings (ENHANCED for PW departments)
        if ('DEPARTMENT' in label_text.upper() and
            any(word in value_text.upper() for word in ['RESOURCE', 'HUMAN', 'STAFF', 'CENTER', 'HEADQUARTERS', 'MINISTRY', 'OFFICE', 'AREA', 'WID', 'PENSIONS', 'KASOA', 'NSAWAM', 'MINISTERS'])):
            score += 1.5  # Bonus for department-related pairings

        # MASSIVE penalty for label-label pairings
        label_indicators = ['SALARY', 'TAX', 'ALLOWANCE', 'DEDUCTION', 'FUND', 'WELFARE', 'EMPLOYEE', 'SSF', 'GHANA', 'SECTION', 'DEPARTMENT', 'JOB', 'BANK', 'ACCOUNT', 'BRANCH', 'NAME', 'NO.', 'ID', 'TITLE']
        label_is_label = any(indicator in label_text.upper() for indicator in label_indicators)
        value_is_label = any(indicator in value_text.upper() for indicator in label_indicators)

        if label_is_label and value_is_label:
            score -= 5.0  # Massive penalty for pairing two labels

        # MASSIVE penalty for value-value pairings (financial amounts)
        if (self._is_likely_financial_amount(label_text) and
            self._is_likely_financial_amount(value_text)):
            score -= 5.0  # Massive penalty for pairing two financial amounts

        # MASSIVE penalty for pairing numbers with numbers
        if (label_text.replace('.', '').replace(',', '').replace(' ', '').isdigit() and
            value_text.replace('.', '').replace(',', '').replace(' ', '').isdigit()):
            score -= 5.0  # Massive penalty for pairing two numbers

        return max(0.0, score)

    def _is_mostly_alphabetic(self, text: str) -> bool:
        """Check if text is mostly alphabetic, allowing department names with hyphens/slashes"""
        if not text:
            return False

        # Remove common department separators and spaces
        clean_text = text.replace(' ', '').replace('-', '').replace('/', '').replace('&', '').replace('.', '')

        # Check if the cleaned text is mostly alphabetic
        if len(clean_text) == 0:
            return False

        alpha_count = sum(1 for c in clean_text if c.isalpha())
        return alpha_count / len(clean_text) >= 0.7  # At least 70% alphabetic

    def _is_section_header_or_decoration(self, text: str) -> bool:
        """Check if text is a section header or decorative element"""
        text_upper = text.upper().strip()

        # Section headers
        if text_upper in ['EARNINGS', 'DEDUCTIONS', 'AMT (GHS)', 'EMPLOYEE BANK DETAILS', 'LOANS', 'EMPLOYER\'S CONTRIBUTIONS']:
            return True

        # Decorative elements (use the comprehensive horizontal separator check)
        if self._is_horizontal_separator(text):
            return True

        return False

    def _are_both_obvious_labels(self, text1: str, text2: str) -> bool:
        """Check if both texts are obviously labels (to avoid label-label pairing)"""

        # Common label patterns
        label_indicators = [
            'EMPLOYEE', 'SSF', 'GHANA', 'SECTION', 'DEPARTMENT', 'JOB', 'TITLE',
            'BANK', 'ACCOUNT', 'BRANCH', 'NAME', 'NO.', 'ID', 'CARD'
        ]

        text1_upper = text1.upper()
        text2_upper = text2.upper()

        # Check if both contain label indicators
        text1_is_label = any(indicator in text1_upper for indicator in label_indicators)
        text2_is_label = any(indicator in text2_upper for indicator in label_indicators)

        return text1_is_label and text2_is_label

    def _determine_label_value_orientation(self, elem1: Dict, elem2: Dict) -> tuple:
        """ENHANCED LABEL-VALUE DETERMINATION WITH STRONG TYPE DETECTION"""

        text1 = elem1['text'].strip()
        text2 = elem2['text'].strip()

        # Strong label indicators
        label_indicators = ['NO.', 'NAME', 'ID', 'TITLE', 'EMPLOYEE', 'SSF', 'GHANA', 'SECTION', 'DEPARTMENT', 'JOB', 'BANK', 'ACCOUNT', 'BRANCH', 'SALARY', 'TAX', 'ALLOWANCE', 'DEDUCTION', 'FUND', 'WELFARE']

        # Strong value indicators
        value_indicators = ['COP', 'SEC', 'GHA-', 'CENTER', 'STAFF', 'OFFICER', 'MANAGER', 'PRINCIPAL', 'ZENITH', 'ECOBANK', 'LTD', 'LIMITED']

        text1_is_label = any(indicator in text1.upper() for indicator in label_indicators)
        text2_is_label = any(indicator in text2.upper() for indicator in label_indicators)

        text1_is_value = (any(indicator in text1.upper() for indicator in value_indicators) or
                         self._is_likely_financial_amount(text1) or
                         text1.upper().startswith(('COP', 'SEC', 'E', 'PW', 'GHA-')))
        text2_is_value = (any(indicator in text2.upper() for indicator in value_indicators) or
                         self._is_likely_financial_amount(text2) or
                         text2.upper().startswith(('COP', 'SEC', 'E', 'PW', 'GHA-')))

        # PREVENT LABEL-LABEL PAIRINGS - BUT ALLOW BANK NAME EXCEPTIONS
        if text1_is_label and text2_is_label:
            # Enhanced exception: Allow bank labels to pair with bank names
            bank_exception = (
                ('BANK' in text1.upper() and len(text1) <= 15 and
                 any(word in text2.upper() for word in ['ZENITH', 'ECOBANK', 'LTD', 'BANK', 'GCB', 'UBA', 'STANBIC', 'FIDELITY', 'CAL', 'REPUBLIC'])) or
                ('BANK' in text2.upper() and len(text2) <= 15 and
                 any(word in text1.upper() for word in ['ZENITH', 'ECOBANK', 'LTD', 'BANK', 'GCB', 'UBA', 'STANBIC', 'FIDELITY', 'CAL', 'REPUBLIC']))
            )

            if bank_exception:
                # Force Bank pairing - treat bank name as value
                if 'BANK' in text1.upper() and len(text1) <= 15:
                    return elem1, elem2  # Bank is label, bank name is value
                else:
                    return elem2, elem1  # Bank name is value, Bank is label

            if not bank_exception:
                # Both are labels - this should never be paired!
                # Return None to indicate invalid pairing
                return None, None

        # SPECIAL CASE: Employee Name pairing - force correct orientation
        if ('NAME' in text1.upper() and
            len(text2.split()) >= 2 and text2.replace(' ', '').replace('-', '').isalpha()):
            return elem1, elem2  # text1 is "Employee Name" label, text2 is the actual name
        elif ('NAME' in text2.upper() and
              len(text1.split()) >= 2 and text1.replace(' ', '').replace('-', '').isalpha()):
            return elem2, elem1  # text2 is "Employee Name" label, text1 is the actual name

        # Strong type-based determination
        if text1_is_label and text2_is_value:
            return elem1, elem2  # elem1 is label, elem2 is value
        elif text2_is_label and text1_is_value:
            return elem2, elem1  # elem2 is label, elem1 is value
        elif text1_is_label and not text2_is_label:
            return elem1, elem2  # elem1 is label, elem2 is value
        elif text2_is_label and not text1_is_label:
            return elem2, elem1  # elem2 is label, elem1 is value

        # If both are labels or both are values, use position
        y_diff = abs(elem1['y'] - elem2['y'])

        if y_diff <= 5:  # Same row (with tolerance)
            if elem1['x'] < elem2['x']:
                return elem1, elem2  # Left is label, Right is value
            else:
                return elem2, elem1  # Right is label, Left is value
        else:  # Different rows
            # For payslips, labels are typically above or to the left of values
            if elem1['y'] < elem2['y']:
                return elem1, elem2  # Top is label, Bottom is value
            else:
                return elem2, elem1  # Bottom is label, Top is value

    def _calculate_label_score(self, text: str, char_stats: Dict) -> float:
        """Calculate how likely text is to be a label"""
        score = 0.0
        text_upper = text.upper()

        # Strong label indicators
        label_keywords = ['EMPLOYEE', 'SSF', 'GHANA', 'SECTION', 'DEPARTMENT', 'JOB', 'BANK', 'ACCOUNT', 'BRANCH', 'NAME', 'TITLE']
        if any(word in text_upper for word in label_keywords):
            score += 5.0

        # Label endings
        if text_upper.endswith(('NO.', 'NAME', 'ID', 'TITLE', 'NO')):
            score += 4.0

        # Descriptive text characteristics
        if char_stats['letter_ratio'] > 0.8:
            score += 2.0

        if char_stats['word_count'] >= 2 and char_stats['letter_ratio'] > 0.7:
            score += 1.5

        # Penalize obvious values
        if self._is_likely_financial_amount(text):
            score -= 5.0

        # Penalize employee numbers, IDs, etc.
        if text_upper.startswith(('COP', 'SEC', 'E', 'PW', 'GHA-')):
            score -= 3.0

        # Penalize pure names (values, not labels)
        if (char_stats['word_count'] >= 2 and char_stats['letter_ratio'] > 0.9 and
            char_stats['digit_count'] == 0 and not any(word in text_upper for word in label_keywords)):
            score -= 2.0

        return score

    def _calculate_value_score(self, text: str, char_stats: Dict) -> float:
        """Calculate how likely text is to be a value"""
        score = 0.0
        text_upper = text.upper()

        # Strong value indicators
        if self._is_likely_financial_amount(text):
            score += 5.0

        # Employee number patterns
        if text_upper.startswith(('COP', 'SEC', 'E', 'PW')) and char_stats['digit_count'] >= 3:
            score += 5.0

        # Ghana Card pattern
        if text_upper.startswith('GHA-') and char_stats['digit_count'] >= 8:
            score += 5.0

        # Personal names (multiple words, mostly letters, no label keywords)
        label_keywords = ['EMPLOYEE', 'SSF', 'GHANA', 'SECTION', 'DEPARTMENT', 'JOB', 'BANK', 'ACCOUNT', 'BRANCH', 'NAME', 'TITLE']
        if (char_stats['word_count'] >= 2 and char_stats['letter_ratio'] > 0.8 and
            char_stats['digit_count'] == 0 and not any(word in text_upper for word in label_keywords)):
            score += 4.0

        # Organization/Department names (data values) - ENHANCED for PW departments
        if any(word in text_upper for word in ['CENTER', 'STAFF', 'OFFICER', 'MANAGER', 'PENTMEDIA', 'PRINCIPAL', 'AREA', 'WID', 'PENSIONS', 'KASOA', 'NSAWAM', 'MINISTERS']):
            score += 3.0

        # ID/Code patterns
        if char_stats['digit_ratio'] > 0.3 and char_stats['letter_ratio'] > 0.1:
            score += 2.0

        # Penalize obvious labels
        if any(word in text_upper for word in label_keywords):
            score -= 5.0

        if text_upper.endswith(('NO.', 'NAME', 'ID', 'TITLE', 'NO')):
            score -= 4.0

        return score

    def _group_elements_by_row(self, elements: List[Dict]) -> Dict[float, List[Dict]]:
        """Group elements by their Y position (rows) with tolerance for slight differences"""
        rows = {}

        for elem in elements:
            y = elem['y']

            # Find if there's an existing row within tolerance (3 pixels)
            found_row = None
            for existing_y in rows.keys():
                if abs(y - existing_y) <= 3.0:  # Increased tolerance for Personal Details
                    found_row = existing_y
                    break

            if found_row is not None:
                rows[found_row].append(elem)
            else:
                rows[y] = [elem]

        # Sort elements in each row by X position
        for y in rows:
            rows[y].sort(key=lambda x: x['x'])

        return rows

    def _find_best_value_match(self, label_elem: Dict, potential_values: List[Dict], rows: Dict, all_elements: List[Dict]) -> Dict:
        """Find the best value match for a label using smart payslip logic"""

        label_x, label_y = label_elem['x'], label_elem['y']
        label_text = label_elem['text'].upper()

        candidates = []

        # Strategy 1: Same row pairing (most common in payslips)
        # Use tolerance for slight Y-coordinate differences
        y_tolerance = 2.0  # Allow 2 pixel difference in Y coordinates

        for elem in potential_values:
            if elem['x'] > label_x:
                y_diff = abs(elem['y'] - label_y)
                if y_diff <= y_tolerance:  # Same row with tolerance
                    distance = elem['x'] - label_x
                    if distance < 200:  # Reasonable distance for personal details
                        confidence = self._calculate_pairing_confidence(label_elem, elem, 'same_row')
                        candidates.append({
                            'value': elem,
                            'type': 'same_row',
                            'distance': distance,
                            'confidence': confidence
                        })

        # Strategy 2: Next row pairing (for vertical layouts)
        for row_y in sorted(rows.keys()):
            if row_y > label_y and row_y - label_y <= 20:  # Within 20 pixels below
                row_elements = rows[row_y]
                for elem in row_elements:
                    if elem in potential_values:
                        x_distance = abs(elem['x'] - label_x)
                        if x_distance <= 50:  # Vertically aligned
                            distance = (row_y - label_y) + x_distance
                            confidence = self._calculate_pairing_confidence(label_elem, elem, 'next_row')
                            candidates.append({
                                'value': elem,
                                'type': 'next_row',
                                'distance': distance,
                                'confidence': confidence
                            })
                break  # Only check the immediate next row

        # Strategy 2.5: Previous row pairing (for cases where value comes before label)
        for row_y in sorted(rows.keys(), reverse=True):
            if row_y < label_y and label_y - row_y <= 20:  # Within 20 pixels above
                row_elements = rows[row_y]
                for elem in row_elements:
                    if elem in potential_values:
                        x_distance = abs(elem['x'] - label_x)
                        if x_distance <= 50:  # Vertically aligned
                            distance = (label_y - row_y) + x_distance
                            confidence = self._calculate_pairing_confidence(label_elem, elem, 'prev_row')
                            candidates.append({
                                'value': elem,
                                'type': 'prev_row',
                                'distance': distance,
                                'confidence': confidence
                            })
                break  # Only check the immediate previous row

        # Strategy 3: Column-based pairing (for structured layouts)
        amount_columns = self._find_amount_columns(all_elements)
        for column_x in amount_columns:
            for elem in potential_values:
                if abs(elem['x'] - column_x) <= 20:  # In amount column
                    y_distance = abs(elem['y'] - label_y)
                    if y_distance <= 15:  # Same row or very close
                        distance = y_distance + abs(elem['x'] - column_x)
                        confidence = self._calculate_pairing_confidence(label_elem, elem, 'column_based')
                        candidates.append({
                            'value': elem,
                            'type': 'column_based',
                            'distance': distance,
                            'confidence': confidence
                        })

        # Return the best candidate
        if candidates:
            return max(candidates, key=lambda x: x['confidence'])

        return None

    def _find_amount_columns(self, elements: List[Dict]) -> List[float]:
        """Find X positions of amount columns"""
        amount_elements = [elem for elem in elements if self._is_likely_financial_amount(elem['text'])]
        if not amount_elements:
            return []

        x_positions = [elem['x'] for elem in amount_elements]
        return self._find_position_clusters(x_positions, threshold=20)

    def _find_position_clusters(self, positions: List[float], threshold: float = 20) -> List[float]:
        """Find clusters of similar positions"""
        if not positions:
            return []

        sorted_positions = sorted(set(positions))
        clusters = []
        current_cluster = [sorted_positions[0]]

        for pos in sorted_positions[1:]:
            if pos - current_cluster[-1] <= threshold:
                current_cluster.append(pos)
            else:
                clusters.append(sum(current_cluster) / len(current_cluster))
                current_cluster = [pos]

        if current_cluster:
            clusters.append(sum(current_cluster) / len(current_cluster))

        return clusters

    def _calculate_pairing_confidence(self, label_elem: Dict, value_elem: Dict, pairing_type: str) -> float:
        """Calculate confidence score for a label-value pairing"""

        label_text = label_elem['text'].upper()
        value_text = value_elem['text']

        confidence = 0.5  # Base confidence

        # Type-based confidence
        if pairing_type == 'same_row':
            confidence += 0.3
        elif pairing_type == 'next_row':
            confidence += 0.2
        elif pairing_type == 'prev_row':
            confidence += 0.25  # Slightly higher than next_row for Personal Details
        elif pairing_type == 'column_based':
            confidence += 0.25

        # Content-based confidence boosts - STATISTICAL ANALYSIS ONLY
        if self._is_likely_financial_amount(value_text):
            # Boost confidence for financial amounts paired with any label
            confidence += 0.15

        # Boost confidence for non-financial values paired with labels
        if not self._is_likely_financial_amount(value_text) and self._is_potential_label(label_text):
            confidence += 0.1

        # Penalize obvious mismatches
        if self._is_potential_label(value_text):
            confidence -= 0.4

        if label_text == value_text:
            confidence -= 0.5

        return max(0.0, min(1.0, confidence))

    def _find_section_patterns(self, elements: List[Dict], section_name: str) -> Dict:
        """Find specific patterns in each section"""

        patterns = {}

        if section_name == 'PERSONAL DETAILS':
            patterns.update(self._find_personal_patterns(elements))
        elif section_name == 'EARNINGS':
            patterns.update(self._find_earnings_patterns(elements))
        elif section_name == 'DEDUCTIONS':
            patterns.update(self._find_deductions_patterns(elements))
        elif section_name == 'LOANS':
            patterns.update(self._find_loans_patterns(elements))
        elif section_name == 'EMPLOYERS CONTRIBUTION':
            patterns.update(self._find_employer_patterns(elements))
        elif section_name == 'EMPLOYEE BANK DETAILS':
            patterns.update(self._find_bank_patterns(elements))

        return patterns

    def _find_personal_patterns(self, elements: List[Dict]) -> Dict:
        """Find personal details patterns using statistical analysis"""
        patterns = {}

        for elem in elements:
            text = elem['text']
            char_stats = self._analyze_text_characteristics(text)

            # Employee number: short alphanumeric code
            if (char_stats['length'] <= 8 and char_stats['digit_ratio'] > 0.4 and
                char_stats['letter_ratio'] > 0.2 and char_stats['symbol_count'] == 0):
                patterns['employee_number'] = elem

            # Ghana card: structured ID with dashes
            elif (char_stats['length'] >= 10 and char_stats['length'] <= 20 and
                  char_stats['digit_ratio'] > 0.7 and '-' in text):
                patterns['ghana_card'] = elem

            # SSF number: long alphanumeric code
            elif (char_stats['length'] >= 10 and char_stats['length'] <= 20 and
                  char_stats['digit_ratio'] > 0.7 and char_stats['letter_ratio'] > 0.1 and
                  char_stats['symbol_count'] == 0):
                patterns['ssf_number'] = elem

            # Employee name: multiple words with letters
            elif (char_stats['word_count'] >= 2 and char_stats['letter_ratio'] > 0.8 and
                  char_stats['digit_count'] == 0 and char_stats['length'] >= 10):
                patterns['employee_name'] = elem

        return patterns

    def _find_earnings_patterns(self, elements: List[Dict]) -> Dict:
        """Find earnings patterns using statistical analysis"""
        patterns = {}

        amounts = [elem for elem in elements if self._is_likely_financial_amount(elem['text'])]

        # Find largest amounts (likely GROSS SALARY)
        if amounts:
            try:
                amounts_with_values = []
                for elem in amounts:
                    # Try to extract numeric value for comparison
                    clean_text = elem['text'].replace(',', '').replace(' ', '')
                    try:
                        value = float(clean_text)
                        amounts_with_values.append((elem, value))
                    except ValueError:
                        # Skip if can't convert to number
                        continue

                if amounts_with_values:
                    amounts_with_values.sort(key=lambda x: x[1], reverse=True)
                    patterns['largest_amount'] = amounts_with_values[0][0]
                    patterns['all_amounts'] = [elem for elem, _ in amounts_with_values]
            except Exception:
                # Fallback: just store all amount elements
                patterns['all_amounts'] = amounts

        return patterns

    def _find_deductions_patterns(self, elements: List[Dict]) -> Dict:
        """Find deductions patterns using statistical analysis"""
        patterns = {}

        amounts = [elem for elem in elements if self._is_likely_financial_amount(elem['text'])]
        patterns['deduction_amounts'] = amounts

        # Look for potential deduction labels based on text characteristics
        for elem in elements:
            text = elem['text']
            char_stats = self._analyze_text_characteristics(text)

            # Deduction labels typically have high letter ratio and descriptive words
            if (char_stats['letter_ratio'] > 0.6 and char_stats['length'] > 5 and
                not self._is_likely_financial_amount(text)):

                # Store potential deduction labels for pattern learning
                if 'potential_labels' not in patterns:
                    patterns['potential_labels'] = []
                patterns['potential_labels'].append(elem)

        return patterns

    def _find_loans_patterns(self, elements: List[Dict]) -> Dict:
        """Find loans patterns using statistical analysis"""
        patterns = {}

        # Look for potential loan-related elements based on text characteristics
        for elem in elements:
            text = elem['text']
            char_stats = self._analyze_text_characteristics(text)

            # Loan labels typically have descriptive text
            if (char_stats['letter_ratio'] > 0.6 and char_stats['length'] > 4 and
                not self._is_likely_financial_amount(text)):

                if 'potential_loan_labels' not in patterns:
                    patterns['potential_loan_labels'] = []
                patterns['potential_loan_labels'].append(elem)

        return patterns

    def _find_employer_patterns(self, elements: List[Dict]) -> Dict:
        """Find employer contribution patterns using statistical analysis"""
        patterns = {}

        for elem in elements:
            text = elem['text']
            char_stats = self._analyze_text_characteristics(text)

            # Employer contribution labels typically have descriptive text
            if (char_stats['letter_ratio'] > 0.6 and char_stats['length'] > 5 and
                not self._is_likely_financial_amount(text)):

                if 'potential_employer_labels' not in patterns:
                    patterns['potential_employer_labels'] = []
                patterns['potential_employer_labels'].append(elem)

        return patterns

    def _extract_loans_tabular_data(self, elements: List[Dict]) -> List[Dict]:
        """SPECIALIZED TABULAR EXTRACTION FOR LOANS SECTION

        Handles the tabular structure where:
        - LOAN column contains loan type names (ECOBANK SCHEME LOAN, RENT ADVANCE)
        - BALANCE B/F, CURRENT DEDUCTION, OUST. BALANCE columns contain financial values

        Creates proper label-value pairs like:
        - "ECOBANK SCHEME LOAN - BALANCE B/F" → "1,575.00"
        - "RENT ADVANCE - CURRENT DEDUCTION" → "250.00"
        """

        if self.debug:
            print(f"   🏦 LOANS TABULAR EXTRACTION: Analyzing {len(elements)} elements")

        # Step 1: Identify column headers and loan type names
        column_headers = []
        loan_type_names = []
        financial_amounts = []

        # Expected column headers in LOANS table
        expected_headers = ['LOAN', 'BALANCE B/F', 'CURRENT DEDUCTION', 'OUST. BALANCE']

        for elem in elements:
            text = elem['text'].strip()
            text_upper = text.upper()

            # Skip section header "LOANS"
            if text_upper == 'LOANS':
                continue

            # Identify column headers - EXACT MATCH ONLY to avoid confusion with loan names
            if text_upper in expected_headers:
                column_headers.append(elem)
                if self.debug:
                    print(f"      📋 Column Header: '{text}' at Y:{elem['y']:.1f} X:{elem['x']:.1f}")

            # Identify financial amounts
            elif self._is_likely_financial_amount(text):
                financial_amounts.append(elem)
                if self.debug:
                    print(f"      💰 Amount: '{text}' at Y:{elem['y']:.1f} X:{elem['x']:.1f}")

            # Identify potential loan type names (descriptive text, not amounts, not exact headers)
            elif (len(text) > 4 and
                  not self._is_likely_financial_amount(text) and
                  text_upper not in expected_headers and  # Changed to exact match
                  not self._is_section_header_or_decoration(text)):

                char_stats = self._analyze_text_characteristics(text)
                # Loan names typically have high letter ratio and multiple words
                if char_stats['letter_ratio'] > 0.6 and char_stats['length'] > 6:
                    loan_type_names.append(elem)
                    if self.debug:
                        print(f"      🏷️  Loan Type: '{text}' at Y:{elem['y']:.1f} X:{elem['x']:.1f}")

        if self.debug:
            print(f"      📊 Found: {len(column_headers)} headers, {len(loan_type_names)} loan types, {len(financial_amounts)} amounts")

        # Step 2: Create tabular structure mapping
        extracted_pairs = []

        if not column_headers or not loan_type_names or not financial_amounts:
            if self.debug:
                print(f"      ⚠️  Insufficient tabular data - falling back to standard pairing")
            # Fallback to standard pairing if tabular structure not detected
            return self._find_label_value_pairs(elements, 'LOANS')

        # Step 3: For each loan type, find values in each column
        for loan_elem in loan_type_names:
            loan_name = loan_elem['text'].strip()
            loan_y = loan_elem['y']

            if self.debug:
                print(f"      🔍 Processing loan: '{loan_name}' at Y:{loan_y:.1f}")

            # Find amounts in the same row as this loan (within Y tolerance)
            row_amounts = []
            y_tolerance = 8.0  # Allow some tolerance for row alignment

            for amount_elem in financial_amounts:
                if abs(amount_elem['y'] - loan_y) <= y_tolerance:
                    row_amounts.append(amount_elem)

            if self.debug:
                print(f"         Found {len(row_amounts)} amounts in same row")

            # Step 4: Match amounts to column headers based on X coordinates
            for amount_elem in row_amounts:
                amount_x = amount_elem['x']
                amount_text = amount_elem['text']

                # Find the closest column header above this amount
                best_header = None
                min_distance = float('inf')

                for header_elem in column_headers:
                    header_x = header_elem['x']
                    header_y = header_elem['y']

                    # Header should be above the amount and reasonably close in X
                    if (header_y < loan_y and  # Header above loan row
                        abs(header_x - amount_x) < 100):  # Reasonable X alignment

                        distance = abs(header_x - amount_x)
                        if distance < min_distance:
                            min_distance = distance
                            best_header = header_elem

                if best_header:
                    header_text = best_header['text'].strip()

                    # Skip if this is the LOAN column (contains loan type names, not amounts)
                    if 'LOAN' in header_text.upper() and header_text.upper() != 'BALANCE B/F':
                        continue

                    # Create composite label: "LOAN_NAME - COLUMN_HEADER"
                    composite_label = f"{loan_name} - {header_text}"

                    # Automatically classify the loan type
                    loan_classification = self._classify_loan_automatically(loan_name)

                    # Create the extracted pair with automatic classification
                    pair = {
                        'label': {
                            'text': composite_label,
                            'x': loan_elem['x'],
                            'y': loan_elem['y']
                        },
                        'value': {
                            'text': amount_text,
                            'x': amount_elem['x'],
                            'y': amount_elem['y']
                        },
                        'pairing_type': 'tabular_loans',
                        'distance': abs(amount_elem['x'] - loan_elem['x']) + abs(amount_elem['y'] - loan_elem['y']),
                        'confidence': 1.0,
                        'loan_type': loan_name,
                        'loan_classification': loan_classification,
                        'column_type': header_text
                    }

                    extracted_pairs.append(pair)

                    if self.debug:
                        print(f"         ✅ PAIRED: '{composite_label}' → '{amount_text}'")
                else:
                    if self.debug:
                        print(f"         ❌ No header found for amount '{amount_text}' at X:{amount_x:.1f}")

        if self.debug:
            print(f"      📋 LOANS TABULAR EXTRACTION: {len(extracted_pairs)} pairs extracted")

        return extracted_pairs

    def _find_bank_patterns(self, elements: List[Dict]) -> Dict:
        """Find bank details patterns using statistical analysis"""
        patterns = {}

        for elem in elements:
            text = elem['text']
            char_stats = self._analyze_text_characteristics(text)

            # Bank labels typically have descriptive text
            if (char_stats['letter_ratio'] > 0.6 and char_stats['length'] > 3 and
                not self._is_likely_financial_amount(text)):

                if 'potential_bank_labels' not in patterns:
                    patterns['potential_bank_labels'] = []
                patterns['potential_bank_labels'].append(elem)

            # Account numbers: long numeric sequences
            elif (char_stats['digit_ratio'] > 0.9 and char_stats['length'] >= 10 and
                  char_stats['length'] <= 25 and char_stats['symbol_count'] == 0):
                patterns['account_number'] = elem

        return patterns

    def _perform_raw_analysis(self, all_elements: List[Dict]) -> Dict:
        """Perform comprehensive raw analysis of all elements"""

        analysis = {
            'coordinate_analysis': self._analyze_coordinates(all_elements),
            'text_analysis': self._analyze_text_patterns(all_elements),
            'layout_analysis': self._analyze_layout(all_elements),
            'font_analysis': self._analyze_fonts(all_elements)
        }

        return analysis

    def _analyze_coordinates(self, elements: List[Dict]) -> Dict:
        """Analyze coordinate distribution"""

        x_coords = [elem['x'] for elem in elements]
        y_coords = [elem['y'] for elem in elements]

        return {
            'x_range': {'min': min(x_coords), 'max': max(x_coords)},
            'y_range': {'min': min(y_coords), 'max': max(y_coords)},
            'unique_x_positions': len(set(x_coords)),
            'unique_y_positions': len(set(y_coords)),
            'common_x_positions': self._find_common_positions(x_coords),
            'common_y_positions': self._find_common_positions(y_coords)
        }

    def _analyze_text_patterns(self, elements: List[Dict]) -> Dict:
        """Analyze text patterns using statistical analysis"""

        all_text = [elem['text'] for elem in elements]

        # Statistical categorization without hardcoded patterns
        patterns = {
            'short_alphanumeric': [],  # Likely employee numbers
            'financial_amounts': [],   # Likely monetary values
            'structured_ids': [],      # Likely ID numbers with separators
            'long_alphanumeric': [],   # Likely SSF numbers
            'numeric_sequences': [],   # Likely account numbers
            'multi_word_text': []      # Likely names
        }

        for text in all_text:
            char_stats = self._analyze_text_characteristics(text)

            # Categorize based on statistical characteristics
            if (char_stats['length'] <= 8 and char_stats['digit_ratio'] > 0.4 and
                char_stats['letter_ratio'] > 0.2 and char_stats['symbol_count'] == 0):
                patterns['short_alphanumeric'].append(text)

            elif self._is_likely_financial_amount(text):
                patterns['financial_amounts'].append(text)

            elif (char_stats['length'] >= 10 and char_stats['digit_ratio'] > 0.7 and
                  char_stats['symbol_count'] > 0):
                patterns['structured_ids'].append(text)

            elif (char_stats['length'] >= 10 and char_stats['digit_ratio'] > 0.7 and
                  char_stats['letter_ratio'] > 0.1 and char_stats['symbol_count'] == 0):
                patterns['long_alphanumeric'].append(text)

            elif (char_stats['digit_ratio'] > 0.9 and char_stats['length'] >= 8 and
                  char_stats['symbol_count'] == 0):
                patterns['numeric_sequences'].append(text)

            elif (char_stats['word_count'] >= 2 and char_stats['letter_ratio'] > 0.8 and
                  char_stats['digit_count'] == 0):
                patterns['multi_word_text'].append(text)

        return patterns

    def _analyze_layout(self, elements: List[Dict]) -> Dict:
        """Analyze layout structure"""

        # Group elements by Y position (rows)
        y_groups = {}
        for elem in elements:
            y_key = round(elem['y'] / 5) * 5  # Group by 5-pixel intervals
            if y_key not in y_groups:
                y_groups[y_key] = []
            y_groups[y_key].append(elem)

        # Analyze columns
        x_positions = sorted(set(elem['x'] for elem in elements))
        column_analysis = self._analyze_columns(x_positions)

        return {
            'row_count': len(y_groups),
            'elements_per_row': {y: len(elems) for y, elems in y_groups.items()},
            'column_analysis': column_analysis,
            'layout_structure': self._determine_layout_structure(y_groups)
        }

    def _analyze_fonts(self, elements: List[Dict]) -> Dict:
        """Analyze font usage"""

        font_sizes = [elem['font_size'] for elem in elements if elem['font_size'] > 0]
        font_flags = [elem['font_flags'] for elem in elements]

        return {
            'font_size_range': {'min': min(font_sizes) if font_sizes else 0, 'max': max(font_sizes) if font_sizes else 0},
            'common_font_sizes': self._find_common_values(font_sizes),
            'font_flags_used': list(set(font_flags)),
            'bold_elements': len([f for f in font_flags if f & 16]),  # Bold flag
            'italic_elements': len([f for f in font_flags if f & 2])   # Italic flag
        }

    def _find_common_positions(self, positions: List[float], threshold: int = 3) -> List[float]:
        """Find commonly used positions"""

        position_counts = {}
        for pos in positions:
            rounded_pos = round(pos, 1)
            position_counts[rounded_pos] = position_counts.get(rounded_pos, 0) + 1

        return [pos for pos, count in position_counts.items() if count >= threshold]

    def _find_common_values(self, values: List[float], threshold: int = 3) -> List[float]:
        """Find commonly used values"""

        value_counts = {}
        for val in values:
            rounded_val = round(val, 1)
            value_counts[rounded_val] = value_counts.get(rounded_val, 0) + 1

        return [val for val, count in value_counts.items() if count >= threshold]

    def _analyze_columns(self, x_positions: List[float]) -> Dict:
        """Analyze column structure"""

        # Find major column boundaries
        major_columns = []
        for i, x in enumerate(x_positions):
            if i == 0 or x - x_positions[i-1] > 50:  # Significant gap
                major_columns.append(x)

        return {
            'total_x_positions': len(x_positions),
            'major_columns': major_columns,
            'column_count': len(major_columns),
            'column_widths': [major_columns[i+1] - major_columns[i] for i in range(len(major_columns)-1)]
        }

    def _determine_layout_structure(self, y_groups: Dict) -> Dict:
        """Determine overall layout structure"""

        structure = {
            'header_rows': [],
            'data_rows': [],
            'footer_rows': []
        }

        sorted_y = sorted(y_groups.keys())

        for y in sorted_y:
            row_elements = y_groups[y]

            if y < 100:  # Header area
                structure['header_rows'].append({'y': y, 'elements': len(row_elements)})
            elif y > 400:  # Footer area
                structure['footer_rows'].append({'y': y, 'elements': len(row_elements)})
            else:  # Data area
                structure['data_rows'].append({'y': y, 'elements': len(row_elements)})

        return structure

    def save_raw_data(self, raw_data: Dict, output_file: str = None) -> str:
        """Save raw data to JSON file"""

        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = raw_data.get('file_info', {}).get('filename', 'unknown')
            page = raw_data.get('file_info', {}).get('page', 1)
            output_file = f"raw_data_{filename.replace('.pdf', '')}_{page}_{timestamp}.json"

        with open(output_file, 'w') as f:
            json.dump(raw_data, f, indent=2)

        print(f"\n💾 Raw data saved to: {output_file}")
        return output_file

    def print_summary(self, raw_data: Dict):
        """Print summary of extracted raw data"""

        print(f"\n📊 RAW DATA EXTRACTION SUMMARY")
        print("=" * 60)

        file_info = raw_data.get('file_info', {})
        print(f"📄 File: {file_info.get('filename', 'Unknown')}")
        print(f"📄 Page: {file_info.get('page', 'Unknown')}")
        print(f"📄 Total Elements: {file_info.get('total_elements', 0)}")

        sections = raw_data.get('sections', {})
        print(f"\n📋 SECTIONS EXTRACTED:")
        for section_name, section_data in sections.items():
            element_count = section_data.get('element_count', 0)
            pairs_count = len(section_data.get('extracted_pairs', []))
            patterns_count = len(section_data.get('patterns_found', {}))

            print(f"   {section_name}: {element_count} elements, {pairs_count} pairs, {patterns_count} patterns")

        # Show key findings
        analysis = raw_data.get('raw_analysis', {})
        text_patterns = analysis.get('text_analysis', {})

        print(f"\n🔍 KEY FINDINGS:")
        if text_patterns.get('short_alphanumeric'):
            print(f"   Short Alphanumeric: {text_patterns['short_alphanumeric']}")
        if text_patterns.get('financial_amounts'):
            print(f"   Financial Amounts: {len(text_patterns['financial_amounts'])} found")
        if text_patterns.get('structured_ids'):
            print(f"   Structured IDs: {text_patterns['structured_ids']}")
        if text_patterns.get('multi_word_text'):
            print(f"   Multi-word Text: {text_patterns['multi_word_text']}")

def main():
    """Main extraction function"""

    print("🔍 SINGLE PAYSLIP RAW DATA EXTRACTOR")
    print("=" * 80)

    # Use default values for demonstration
    pdf_file = "JONE.pdf"
    page_num = 1

    print(f"📄 Extracting raw data from: {pdf_file} (Page {page_num})")
    print()

    # Extract raw data
    extractor = PerfectSectionAwareExtractor(debug=True)
    raw_data = extractor.extract_raw_data(pdf_file, page_num)

    if 'error' not in raw_data:
        # Print summary
        extractor.print_summary(raw_data)

        # Save to file
        output_file = extractor.save_raw_data(raw_data)

        print(f"\n✅ Raw data extraction completed!")
        print(f"📁 Data saved to: {output_file}")
    else:
        print(f"❌ Extraction failed: {raw_data['error']}")

if __name__ == "__main__":
    main()