#!/usr/bin/env python3
"""
Check Latest Extraction Results
Check if the re-run job created new extraction data with department information
"""

import os
import sys
import sqlite3
from pathlib import Path
from datetime import datetime, timedelta

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_latest_extraction():
    """Check for latest extraction results after re-run"""
    print("🔍 CHECKING LATEST EXTRACTION RESULTS")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK MOST RECENT SESSIONS (last 2 hours)
        cutoff_time = (datetime.now() - timedelta(hours=2)).strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"📊 CHECKING SESSIONS CREATED AFTER: {cutoff_time}")
        
        # Check extracted_data for recent sessions
        cursor.execute("""
            SELECT session_id, COUNT(*) as records, 
                   COUNT(CASE WHEN item_label = 'DEPARTMENT' THEN 1 END) as dept_records,
                   MIN(created_at) as first_record,
                   MAX(created_at) as last_record
            FROM extracted_data 
            WHERE created_at > ?
            GROUP BY session_id
            ORDER BY last_record DESC
        """, (cutoff_time,))
        
        recent_sessions = cursor.fetchall()
        
        if recent_sessions:
            print(f"\n✅ FOUND {len(recent_sessions)} RECENT EXTRACTION SESSIONS:")
            
            for session_id, total_records, dept_records, first_record, last_record in recent_sessions:
                print(f"\n   📋 {session_id}:")
                print(f"      Total records: {total_records}")
                print(f"      Department records: {dept_records}")
                print(f"      Time range: {first_record} to {last_record}")
                
                if dept_records > 0:
                    # Check our missing employees in this session
                    missing_employees = ['PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617']
                    
                    print(f"      🔍 Checking missing employees:")
                    for emp_id in missing_employees:
                        cursor.execute("""
                            SELECT item_value
                            FROM extracted_data 
                            WHERE session_id = ? AND employee_id = ? AND item_label = 'DEPARTMENT'
                        """, (session_id, emp_id))
                        
                        dept_result = cursor.fetchone()
                        if dept_result:
                            print(f"         ✅ {emp_id}: {dept_result[0]}")
                        else:
                            # Check if employee exists at all in this session
                            cursor.execute("""
                                SELECT COUNT(*)
                                FROM extracted_data 
                                WHERE session_id = ? AND employee_id = ?
                            """, (session_id, emp_id))
                            
                            emp_count = cursor.fetchone()[0]
                            if emp_count > 0:
                                print(f"         ⚠️ {emp_id}: exists but no department")
                            else:
                                print(f"         ❌ {emp_id}: not found")
                else:
                    print(f"      ❌ No department records in this session")
        else:
            print(f"\n❌ NO RECENT EXTRACTION SESSIONS FOUND")
            print(f"   The re-run may not have completed yet or used a different session naming")
        
        # 2. CHECK EMPLOYEES TABLE FOR RECENT UPDATES
        print(f"\n2. 📋 CHECKING EMPLOYEES TABLE FOR RECENT UPDATES:")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as total,
                   COUNT(CASE WHEN department IS NOT NULL AND department != '' THEN 1 END) as with_dept,
                   MIN(created_at) as first_record,
                   MAX(created_at) as last_record
            FROM employees 
            WHERE created_at > ?
            GROUP BY session_id
            ORDER BY last_record DESC
        """, (cutoff_time,))
        
        recent_employee_sessions = cursor.fetchall()
        
        if recent_employee_sessions:
            print(f"\n✅ FOUND {len(recent_employee_sessions)} RECENT EMPLOYEE SESSIONS:")
            
            for session_id, total, with_dept, first_record, last_record in recent_employee_sessions:
                print(f"\n   📋 {session_id}:")
                print(f"      Total employees: {total}")
                print(f"      With departments: {with_dept}")
                print(f"      Time range: {first_record} to {last_record}")
                
                # Check our missing employees
                missing_employees = ['PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617']
                
                print(f"      🔍 Checking missing employees:")
                for emp_id in missing_employees:
                    cursor.execute("""
                        SELECT department
                        FROM employees 
                        WHERE session_id = ? AND employee_id = ?
                    """, (session_id, emp_id))
                    
                    emp_result = cursor.fetchone()
                    if emp_result:
                        dept = emp_result[0] if emp_result[0] else "NULL/EMPTY"
                        print(f"         ✅ {emp_id}: {dept}")
                    else:
                        print(f"         ❌ {emp_id}: not found")
        else:
            print(f"\n❌ NO RECENT EMPLOYEE UPDATES FOUND")
        
        # 3. CHECK ALL SESSIONS TO FIND THE LATEST
        print(f"\n3. 📋 CHECKING ALL SESSIONS (LATEST FIRST):")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as dept_records,
                   MAX(created_at) as latest_update
            FROM extracted_data 
            WHERE item_label = 'DEPARTMENT'
            GROUP BY session_id
            ORDER BY latest_update DESC
            LIMIT 5
        """)
        
        all_dept_sessions = cursor.fetchall()
        
        if all_dept_sessions:
            print(f"\n📊 LATEST SESSIONS WITH DEPARTMENT DATA:")
            
            for session_id, dept_records, latest_update in all_dept_sessions:
                print(f"   {session_id}: {dept_records} dept records (last: {latest_update})")
                
                # Check if this is newer than our current assessment
                if latest_update > cutoff_time:
                    print(f"      🎯 THIS IS A NEW SESSION - SHOULD BE USED FOR ASSESSMENT")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_latest_extraction()
    sys.exit(0 if success else 1)
