#!/usr/bin/env python3
"""
Debug Department Lookup
Find the real issue with department lookup - not extraction
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def debug_department_lookup():
    """Debug the real department lookup issue"""
    print("🔍 DEBUGGING DEPARTMENT LOOKUP - REAL ISSUE")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK WHAT SESSIONS EXIST
        print("1. 📋 ALL SESSIONS IN DATABASE:")
        
        # Check extracted_data sessions
        cursor.execute("""
            SELECT session_id, COUNT(DISTINCT employee_id) as employees,
                   COUNT(CASE WHEN item_label = 'DEPARTMENT' THEN 1 END) as dept_records
            FROM extracted_data
            GROUP BY session_id
            ORDER BY MAX(created_at) DESC
        """)
        
        extracted_sessions = cursor.fetchall()
        
        print(f"   📊 EXTRACTED_DATA SESSIONS:")
        for session_id, employees, dept_records in extracted_sessions:
            print(f"      {session_id}: {employees} employees, {dept_records} dept records")
        
        # Check employees table sessions
        cursor.execute("""
            SELECT session_id, COUNT(*) as employees,
                   COUNT(CASE WHEN department IS NOT NULL AND department != '' THEN 1 END) as with_dept
            FROM employees
            GROUP BY session_id
            ORDER BY COUNT(*) DESC
        """)
        
        employee_sessions = cursor.fetchall()
        
        print(f"\n   📊 EMPLOYEES TABLE SESSIONS:")
        for session_id, employees, with_dept in employee_sessions:
            print(f"      {session_id}: {employees} employees, {with_dept} with departments")
        
        # 2. CHECK SPECIFIC EMPLOYEES IN ALL SOURCES
        print(f"\n2. 📋 CHECKING SPECIFIC EMPLOYEES IN ALL SOURCES:")
        
        test_employees = ['PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617']
        
        for emp_id in test_employees:
            print(f"\n   🔍 {emp_id}:")
            
            # Check in employees table
            cursor.execute("""
                SELECT session_id, department
                FROM employees
                WHERE employee_id = ?
                AND department IS NOT NULL 
                AND department != ''
                ORDER BY created_at DESC
            """, (emp_id,))
            
            emp_results = cursor.fetchall()
            if emp_results:
                print(f"      ✅ EMPLOYEES TABLE:")
                for session, dept in emp_results:
                    print(f"         {session}: {dept}")
            else:
                print(f"      ❌ EMPLOYEES TABLE: Not found")
            
            # Check in extracted_data
            cursor.execute("""
                SELECT session_id, item_value
                FROM extracted_data
                WHERE employee_id = ? AND item_label = 'DEPARTMENT'
                ORDER BY created_at DESC
            """, (emp_id,))
            
            ext_results = cursor.fetchall()
            if ext_results:
                print(f"      ✅ EXTRACTED_DATA:")
                for session, dept in ext_results:
                    print(f"         {session}: {dept}")
            else:
                print(f"      ❌ EXTRACTED_DATA: Not found")
        
        # 3. CHECK TRACKER RESULTS TABLE
        print(f"\n3. 📋 CHECKING TRACKER RESULTS:")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as items
            FROM tracker_results
            GROUP BY session_id
            ORDER BY session_id DESC
        """)
        
        tracker_sessions = cursor.fetchall()
        
        if tracker_sessions:
            print(f"   📊 TRACKER SESSIONS:")
            for session_id, items in tracker_sessions:
                print(f"      {session_id}: {items} items")
                
                # Check if our test employees are in tracker
                for emp_id in test_employees[:2]:  # Check first 2
                    cursor.execute("""
                        SELECT employee_name, department
                        FROM tracker_results
                        WHERE session_id = ? AND employee_id = ?
                    """, (session_id, emp_id))
                    
                    tracker_result = cursor.fetchone()
                    if tracker_result:
                        name, dept = tracker_result
                        print(f"         {emp_id}: {name} - {dept}")
        else:
            print(f"   ❌ No tracker results found")
        
        # 4. TEST THE ACTUAL DEPARTMENT LOOKUP FUNCTION
        print(f"\n4. 📋 TESTING DEPARTMENT LOOKUP FUNCTION:")
        
        # Import the function and test it directly
        try:
            sys.path.append(os.getcwd())
            from bank_adviser_tracker_operations import get_employee_department
            
            for emp_id in test_employees:
                try:
                    department = get_employee_department(cursor, emp_id, "test_session")
                    print(f"   ✅ {emp_id}: {department}")
                except Exception as e:
                    print(f"   ❌ {emp_id}: {str(e)}")
        
        except ImportError as e:
            print(f"   ❌ Could not import function: {e}")
        
        # 5. CHECK SESSION MISMATCH ISSUE
        print(f"\n5. 📋 CHECKING SESSION MISMATCH:")
        
        # What session is the tracker trying to use?
        cursor.execute("""
            SELECT DISTINCT session_id
            FROM tracker_results
            ORDER BY session_id DESC
            LIMIT 1
        """)
        
        tracker_session_result = cursor.fetchone()
        if tracker_session_result:
            tracker_session = tracker_session_result[0]
            print(f"   📊 Tracker using session: {tracker_session}")
            
            # Check if this session has department data
            cursor.execute("""
                SELECT COUNT(DISTINCT employee_id) as employees_with_dept
                FROM extracted_data
                WHERE session_id = ? AND item_label = 'DEPARTMENT'
            """, (tracker_session,))
            
            dept_count = cursor.fetchone()[0]
            print(f"   📊 Department records in tracker session: {dept_count}")
            
            if dept_count == 0:
                print(f"   🎯 ISSUE FOUND: Tracker session has NO department data!")
                print(f"   📋 Need to use a different session with department data")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_department_lookup()
    sys.exit(0 if success else 1)
