#!/usr/bin/env python3
"""
Find Actual Processing Limits
Find where the system is actually limiting payslip processing to ~2967 instead of 5919
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def find_actual_limits():
    """Find where the actual processing limits are occurring"""
    print("🔍 FINDING ACTUAL PROCESSING LIMITS")
    print("=" * 60)
    print("📊 EXPECTED: 5919 payslips (2960 July + 2959 June)")
    print("📊 ACTUAL: ~2967 payslips extracted")
    print("📊 MISSING: ~2952 payslips (50% of total)")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK EXTRACTION SESSION DETAILS
        print("1. 📋 EXTRACTION SESSION ANALYSIS:")
        
        # Get latest extraction session details
        cursor.execute("""
            SELECT session_id, 
                   COUNT(DISTINCT employee_id) as unique_employees,
                   COUNT(*) as total_records,
                   MIN(created_at) as start_time,
                   MAX(created_at) as end_time
            FROM extracted_data
            GROUP BY session_id
            ORDER BY MAX(created_at) DESC
            LIMIT 1
        """)
        
        latest_session = cursor.fetchone()
        if latest_session:
            session_id, employees, records, start_time, end_time = latest_session
            print(f"\n   📊 Latest Session: {session_id}")
            print(f"      Employees: {employees}")
            print(f"      Records: {records}")
            print(f"      Duration: {start_time} to {end_time}")
            
            # Calculate records per employee
            avg_records = records / employees if employees > 0 else 0
            print(f"      Avg records per employee: {avg_records:.1f}")
            
            # Expected records for 5919 employees
            expected_records = 5919 * avg_records
            print(f"      Expected total records for 5919 employees: {expected_records:.0f}")
            print(f"      Missing records: {expected_records - records:.0f}")
        
        # 2. CHECK FOR PROCESSING CUTOFFS
        print(f"\n2. 📋 PROCESSING CUTOFF ANALYSIS:")
        
        if latest_session:
            session_id = latest_session[0]
            
            # Check if there's a pattern in employee IDs that suggests a cutoff
            cursor.execute("""
                SELECT employee_id, COUNT(*) as record_count
                FROM extracted_data
                WHERE session_id = ?
                GROUP BY employee_id
                ORDER BY employee_id
                LIMIT 10
            """, (session_id,))
            
            first_employees = cursor.fetchall()
            print(f"   📊 First 10 employees:")
            for emp_id, count in first_employees:
                print(f"      {emp_id}: {count} records")
            
            cursor.execute("""
                SELECT employee_id, COUNT(*) as record_count
                FROM extracted_data
                WHERE session_id = ?
                GROUP BY employee_id
                ORDER BY employee_id DESC
                LIMIT 10
            """, (session_id,))
            
            last_employees = cursor.fetchall()
            print(f"\n   📊 Last 10 employees:")
            for emp_id, count in last_employees:
                print(f"      {emp_id}: {count} records")
            
            # Check for gaps in employee ID ranges
            cursor.execute("""
                SELECT MIN(employee_id) as min_id, MAX(employee_id) as max_id
                FROM extracted_data
                WHERE session_id = ?
            """, (session_id,))
            
            id_range = cursor.fetchone()
            if id_range:
                min_id, max_id = id_range
                print(f"\n   📊 Employee ID Range: {min_id} to {max_id}")
        
        # 3. CHECK FOR MEMORY OR TIMEOUT ISSUES
        print(f"\n3. 📋 EXTRACTION PATTERN ANALYSIS:")
        
        if latest_session:
            session_id = latest_session[0]
            
            # Check extraction timing patterns
            cursor.execute("""
                SELECT 
                    strftime('%H:%M:%S', created_at) as time_stamp,
                    COUNT(*) as records_at_time
                FROM extracted_data
                WHERE session_id = ?
                GROUP BY strftime('%H:%M:%S', created_at)
                ORDER BY time_stamp
            """, (session_id,))
            
            time_pattern = cursor.fetchall()
            print(f"   📊 Extraction timing pattern:")
            for time_stamp, count in time_pattern[:10]:
                print(f"      {time_stamp}: {count} records")
            
            if len(time_pattern) > 10:
                print(f"      ... and {len(time_pattern) - 10} more time points")
            
            # Check if extraction stopped abruptly
            if len(time_pattern) > 1:
                first_time = time_pattern[0][0]
                last_time = time_pattern[-1][0]
                print(f"\n   📊 Extraction timespan: {first_time} to {last_time}")
                
                # Check if there's a sudden drop in records
                max_records = max([count for _, count in time_pattern])
                last_records = time_pattern[-1][1]
                
                if last_records < max_records * 0.5:
                    print(f"   ⚠️ POTENTIAL CUTOFF: Last time point has {last_records} records vs max {max_records}")
        
        # 4. CHECK PDF FILE INFORMATION
        print(f"\n4. 📋 PDF FILE ANALYSIS:")
        
        # Look for any PDF-related information in the database
        cursor.execute("""
            SELECT DISTINCT session_id, COUNT(*) as employee_count
            FROM employees
            GROUP BY session_id
            ORDER BY employee_count DESC
            LIMIT 5
        """)
        
        employee_sessions = cursor.fetchall()
        print(f"   📊 Employee sessions:")
        for session, count in employee_sessions:
            print(f"      {session}: {count} employees")
        
        # 5. IDENTIFY THE EXACT LIMITATION
        print(f"\n5. 🎯 LIMITATION IDENTIFICATION:")
        print("=" * 50)
        
        if latest_session:
            employees_extracted = latest_session[1]
            expected_employees = 5919
            missing_employees = expected_employees - employees_extracted
            
            print(f"   📊 FACTS:")
            print(f"      Expected: {expected_employees} employees")
            print(f"      Extracted: {employees_extracted} employees")
            print(f"      Missing: {missing_employees} employees ({missing_employees/expected_employees*100:.1f}%)")
            print(f"      Extraction ratio: {employees_extracted/expected_employees:.3f}")
            
            # Check if it's exactly half
            if abs(employees_extracted - expected_employees/2) < 100:
                print(f"\n   🎯 PATTERN: Extracted approximately HALF of expected employees")
                print(f"      This suggests a systematic limitation, not random failure")
            
            # Check if it matches any common limits
            common_limits = [1000, 2000, 2500, 3000, 5000]
            for limit in common_limits:
                if abs(employees_extracted - limit) < 50:
                    print(f"\n   🎯 POTENTIAL LIMIT: Close to {limit} employee limit")
            
            print(f"\n   🔍 LIKELY CAUSES:")
            print(f"      1. PDF file only contains ~{employees_extracted} pages")
            print(f"      2. Extraction process has hidden maxPages limit")
            print(f"      3. Memory/timeout causing early termination")
            print(f"      4. Batch processing stopping after certain number")
            print(f"      5. PDF corruption after page ~{employees_extracted}")
            
            print(f"\n   📋 IMMEDIATE ACTIONS:")
            print(f"      1. Check actual PDF file page count")
            print(f"      2. Look for maxPages parameters in extraction calls")
            print(f"      3. Check extraction logs for errors/timeouts")
            print(f"      4. Verify PDF file integrity")
            print(f"      5. Test extraction with debug mode enabled")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = find_actual_limits()
    sys.exit(0 if success else 1)
