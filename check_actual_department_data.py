#!/usr/bin/env python3
"""
Check Actual Department Data in Database
Find where the department data is actually stored and why it's not being found
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_department_data():
    """Check where department data is actually stored"""
    print("🔍 CHECKING ACTUAL DEPARTMENT DATA STORAGE")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Get the session with tracker data
        session_id = "audit_session_1751122785_5479febb"
        print(f"📊 Checking session: {session_id}")
        
        # 1. Check all tables that might contain department data
        print(f"\n1. 📋 CHECKING ALL TABLES FOR DEPARTMENT DATA:")
        
        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        department_tables = []
        for table in tables:
            try:
                # Check if table has department column
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [col[1] for col in cursor.fetchall()]
                
                if 'department' in columns:
                    department_tables.append(table)
                    print(f"   ✅ {table}: has department column")
                    
                    # Check if there's data for our session
                    if 'session_id' in columns:
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE session_id = ?", (session_id,))
                        count = cursor.fetchone()[0]
                        print(f"      📊 Records in session: {count}")
                        
                        if count > 0:
                            # Sample some data
                            cursor.execute(f"SELECT employee_id, department FROM {table} WHERE session_id = ? LIMIT 5", (session_id,))
                            samples = cursor.fetchall()
                            for emp_id, dept in samples:
                                print(f"      📋 {emp_id}: {dept}")
                    else:
                        # Check total records
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        print(f"      📊 Total records: {count}")
                        
                        if count > 0:
                            cursor.execute(f"SELECT employee_id, department FROM {table} LIMIT 5")
                            samples = cursor.fetchall()
                            for emp_id, dept in samples:
                                print(f"      📋 {emp_id}: {dept}")
                                
            except Exception as e:
                print(f"   ❌ {table}: Error checking - {e}")
        
        # 2. Check extracted_data table specifically
        print(f"\n2. 📋 CHECKING EXTRACTED_DATA TABLE:")
        
        try:
            # Check for department-related items in extracted_data
            cursor.execute("""
                SELECT DISTINCT item_label 
                FROM extracted_data 
                WHERE session_id = ? 
                AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%' 
                     OR item_label LIKE '%SECTION%' OR item_label LIKE '%DIVISION%')
            """, (session_id,))
            
            dept_labels = cursor.fetchall()
            if dept_labels:
                print("   ✅ Found department-related labels:")
                for label in dept_labels:
                    print(f"      📋 {label[0]}")
                    
                    # Get sample data for this label
                    cursor.execute("""
                        SELECT employee_id, item_value 
                        FROM extracted_data 
                        WHERE session_id = ? AND item_label = ?
                        LIMIT 5
                    """, (session_id, label[0]))
                    
                    samples = cursor.fetchall()
                    for emp_id, value in samples:
                        print(f"         {emp_id}: {value}")
            else:
                print("   ❌ No department-related labels found")
                
                # Check what labels do exist
                cursor.execute("""
                    SELECT DISTINCT item_label 
                    FROM extracted_data 
                    WHERE session_id = ?
                    ORDER BY item_label
                """, (session_id,))
                
                all_labels = cursor.fetchall()
                print(f"   📋 Available labels ({len(all_labels)}):")
                for i, label in enumerate(all_labels[:20]):  # Show first 20
                    print(f"      {label[0]}")
                if len(all_labels) > 20:
                    print(f"      ... and {len(all_labels) - 20} more")
                    
        except Exception as e:
            print(f"   ❌ Error checking extracted_data: {e}")
        
        # 3. Check if there are any employees with departments in ANY session
        print(f"\n3. 📋 CHECKING FOR DEPARTMENTS IN ANY SESSION:")
        
        for table in department_tables:
            try:
                cursor.execute(f"""
                    SELECT session_id, COUNT(*) as count, 
                           COUNT(CASE WHEN department IS NOT NULL AND department != '' THEN 1 END) as with_dept
                    FROM {table} 
                    GROUP BY session_id 
                    ORDER BY with_dept DESC
                    LIMIT 5
                """)
                
                sessions = cursor.fetchall()
                if sessions:
                    print(f"   📊 {table} - Sessions with department data:")
                    for sess_id, total, with_dept in sessions:
                        print(f"      {sess_id}: {with_dept}/{total} have departments")
                        
                        if with_dept > 0:
                            # Sample department data from this session
                            cursor.execute(f"""
                                SELECT employee_id, department 
                                FROM {table} 
                                WHERE session_id = ? AND department IS NOT NULL AND department != ''
                                LIMIT 3
                            """, (sess_id,))
                            
                            dept_samples = cursor.fetchall()
                            for emp_id, dept in dept_samples:
                                print(f"         {emp_id}: {dept}")
                                
            except Exception as e:
                print(f"   ❌ Error checking {table}: {e}")
        
        conn.close()
        
        print(f"\n🎯 ANALYSIS COMPLETE")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        return False

def suggest_solution():
    """Suggest solution based on findings"""
    print("\n💡 SOLUTION STRATEGY:")
    print("=" * 30)
    print("1. If department data exists in other sessions:")
    print("   - Check extraction process for current session")
    print("   - Verify PDF contains department information")
    print("   - Re-run extraction if needed")
    print()
    print("2. If department data is in extracted_data but not employees table:")
    print("   - Fix the data transfer process")
    print("   - Update department lookup to use extracted_data")
    print()
    print("3. If no department data exists anywhere:")
    print("   - Check PDF source for department information")
    print("   - Verify extraction process captures department fields")
    print("   - Update extraction patterns if needed")

if __name__ == "__main__":
    success = check_department_data()
    if success:
        suggest_solution()
    sys.exit(0 if success else 1)
