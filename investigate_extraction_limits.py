#!/usr/bin/env python3
"""
Investigate Extraction Limits and Filtering
Find the root cause of why only 2967 out of 5919 payslips were extracted
"""

import os
import sys
import sqlite3
import json
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_extraction_limits():
    """Investigate extraction limits and filtering settings"""
    print("🔍 INVESTIGATING EXTRACTION LIMITS AND FILTERING")
    print("=" * 70)
    
    try:
        # 1. CHECK SETTINGS FILES
        print("1. 📋 CHECKING CONFIGURATION FILES:")
        
        settings_files = [
            "data/settings.json",
            "settings.json",
            "config.json",
            "extraction_config.json"
        ]
        
        for settings_file in settings_files:
            if os.path.exists(settings_file):
                print(f"\n   📄 Found: {settings_file}")
                try:
                    with open(settings_file, 'r') as f:
                        settings = json.load(f)
                    
                    print(f"      📊 Settings content:")
                    for key, value in settings.items():
                        print(f"         {key}: {value}")
                        
                        # Check for limiting settings
                        if isinstance(value, dict):
                            for subkey, subvalue in value.items():
                                if any(limit_word in subkey.lower() for limit_word in ['max', 'limit', 'threshold', 'batch']):
                                    print(f"            🎯 POTENTIAL LIMIT: {subkey} = {subvalue}")
                except Exception as e:
                    print(f"      ❌ Error reading {settings_file}: {e}")
            else:
                print(f"   ❌ Not found: {settings_file}")
        
        # 2. CHECK DATABASE FOR EXTRACTION SESSIONS AND LIMITS
        print(f"\n2. 📋 CHECKING DATABASE FOR EXTRACTION EVIDENCE:")
        
        db_path = get_database_path()
        if not db_path:
            print("   ❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Check for extraction session metadata
        cursor.execute("""
            SELECT DISTINCT session_id, 
                   COUNT(DISTINCT employee_id) as employees,
                   MIN(created_at) as start_time,
                   MAX(created_at) as end_time,
                   COUNT(*) as total_records
            FROM extracted_data
            GROUP BY session_id
            ORDER BY start_time DESC
            LIMIT 5
        """)
        
        sessions = cursor.fetchall()
        print(f"\n   📊 Recent extraction sessions:")
        for session_id, employees, start_time, end_time, total_records in sessions:
            duration = "Unknown"
            if start_time and end_time:
                from datetime import datetime
                try:
                    start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                    duration = str(end - start)
                except:
                    pass
            
            print(f"      {session_id}:")
            print(f"         Employees: {employees}")
            print(f"         Records: {total_records}")
            print(f"         Duration: {duration}")
            print(f"         Time: {start_time} to {end_time}")
        
        # 3. CHECK FOR PAGE PROCESSING EVIDENCE
        print(f"\n3. 📋 CHECKING PAGE PROCESSING PATTERNS:")
        
        # Check if there's page number information
        cursor.execute("PRAGMA table_info(extracted_data)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'page_number' in columns:
            print(f"   ✅ Page number tracking available")
            
            # Get page distribution for latest session
            latest_session = sessions[0][0] if sessions else None
            if latest_session:
                cursor.execute("""
                    SELECT page_number, COUNT(DISTINCT employee_id) as employees
                    FROM extracted_data
                    WHERE session_id = ? AND page_number IS NOT NULL
                    GROUP BY page_number
                    ORDER BY page_number
                    LIMIT 20
                """, (latest_session,))
                
                page_data = cursor.fetchall()
                if page_data:
                    print(f"   📊 Page distribution (first 20 pages):")
                    for page_num, emp_count in page_data:
                        print(f"      Page {page_num}: {emp_count} employees")
                    
                    # Check for gaps or cutoffs
                    max_page = max([p[0] for p in page_data])
                    print(f"   📊 Maximum page processed: {max_page}")
                    
                    if max_page < 5919:  # Expected total
                        print(f"   🎯 POTENTIAL ISSUE: Only processed {max_page} pages, expected ~5919")
                else:
                    print(f"   ❌ No page data found")
        else:
            print(f"   ❌ No page number tracking in extracted_data")
        
        # 4. CHECK FOR BATCH PROCESSING EVIDENCE
        print(f"\n4. 📋 CHECKING BATCH PROCESSING EVIDENCE:")
        
        # Look for batch-related patterns in timestamps
        if sessions:
            latest_session = sessions[0][0]
            cursor.execute("""
                SELECT
                    DATE(created_at) as extraction_date,
                    strftime('%H', created_at) as extraction_hour,
                    COUNT(DISTINCT employee_id) as employees,
                    COUNT(*) as records
                FROM extracted_data
                WHERE session_id = ?
                GROUP BY DATE(created_at), strftime('%H', created_at)
                ORDER BY extraction_date, extraction_hour
            """, (latest_session,))
            
            batch_data = cursor.fetchall()
            if batch_data:
                print(f"   📊 Extraction timeline:")
                for date, hour, employees, records in batch_data:
                    print(f"      {date} {hour:02d}:00: {employees} employees, {records} records")
        
        # 5. CHECK FOR FILTERING EVIDENCE
        print(f"\n5. 📋 CHECKING FOR FILTERING EVIDENCE:")
        
        # Check if certain employee types are missing
        cursor.execute("""
            SELECT 
                SUBSTR(employee_id, 1, 3) as prefix,
                COUNT(DISTINCT employee_id) as count
            FROM extracted_data
            GROUP BY SUBSTR(employee_id, 1, 3)
            ORDER BY count DESC
        """)
        
        prefix_data = cursor.fetchall()
        print(f"   📊 Employee ID prefixes:")
        for prefix, count in prefix_data:
            print(f"      {prefix}: {count} employees")
        
        # Check for missing ranges
        cursor.execute("""
            SELECT MIN(employee_id) as min_id, MAX(employee_id) as max_id
            FROM extracted_data
            WHERE employee_id LIKE 'COP%'
        """)
        
        cop_range = cursor.fetchone()
        if cop_range and cop_range[0]:
            print(f"   📊 COP employee range: {cop_range[0]} to {cop_range[1]}")
        
        # 6. CHECK EXTRACTION LOGS OR ERROR PATTERNS
        print(f"\n6. 📋 CHECKING FOR ERROR PATTERNS:")
        
        # Look for any error indicators in the data
        cursor.execute("""
            SELECT item_label, COUNT(*) as count
            FROM extracted_data
            WHERE item_label LIKE '%ERROR%' OR item_label LIKE '%FAILED%'
            GROUP BY item_label
        """)
        
        error_data = cursor.fetchall()
        if error_data:
            print(f"   ⚠️ Error indicators found:")
            for label, count in error_data:
                print(f"      {label}: {count} occurrences")
        else:
            print(f"   ✅ No error indicators in extracted data")
        
        conn.close()
        
        # 7. FINAL ANALYSIS
        print(f"\n7. 🎯 ROOT CAUSE ANALYSIS:")
        print("=" * 50)
        
        if sessions:
            latest_employees = sessions[0][1]
            expected_employees = 5919
            missing_employees = expected_employees - latest_employees
            
            print(f"   📊 Latest extraction: {latest_employees} employees")
            print(f"   📊 Expected: {expected_employees} employees")
            print(f"   📊 Missing: {missing_employees} employees ({missing_employees/expected_employees*100:.1f}%)")
            
            print(f"\n   🔍 POSSIBLE ROOT CAUSES:")
            print(f"      1. maxPages limit set to ~{latest_employees} in extraction settings")
            print(f"      2. Batch processing stopped early due to timeout or error")
            print(f"      3. PDF file corruption or unreadable pages after page {latest_employees}")
            print(f"      4. Memory limits causing extraction to stop")
            print(f"      5. Employee ID filtering excluding certain ranges")
            print(f"      6. Extraction process interrupted or killed")
            
            print(f"\n   📋 RECOMMENDED INVESTIGATION:")
            print(f"      1. Check extraction logs for timeout or memory errors")
            print(f"      2. Verify PDF file integrity and total page count")
            print(f"      3. Check for maxPages or batch size limits in settings")
            print(f"      4. Test extraction with unlimited settings")
            print(f"      5. Monitor memory usage during extraction")
        
        return True
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = investigate_extraction_limits()
    sys.exit(0 if success else 1)
