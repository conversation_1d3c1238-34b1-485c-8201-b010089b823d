#!/usr/bin/env python3
"""
Check Database Extraction Data
Verify the actual extracted data in the database after the re-run
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_database_extraction():
    """Check the actual extraction data in the database"""
    print("🔍 CHECKING DATABASE EXTRACTION DATA")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK LATEST EXTRACTION SESSIONS
        print("1. 📋 LATEST EXTRACTION SESSIONS:")
        
        cursor.execute("""
            SELECT session_id, 
                   COUNT(DISTINCT employee_id) as unique_employees,
                   COUNT(*) as total_records,
                   MIN(created_at) as start_time,
                   MAX(created_at) as end_time
            FROM extracted_data
            GROUP BY session_id
            ORDER BY MAX(created_at) DESC
            LIMIT 5
        """)
        
        sessions = cursor.fetchall()
        
        latest_session = None
        
        for session_id, employees, records, start_time, end_time in sessions:
            print(f"\n   📊 {session_id}:")
            print(f"      Employees: {employees}")
            print(f"      Records: {records}")
            print(f"      Time: {start_time} to {end_time}")
            
            if not latest_session:
                latest_session = session_id
            
            # Check if this is the complete extraction
            if employees >= 5800:
                print(f"      ✅ COMPLETE EXTRACTION (expected ~5919)")
            elif employees >= 2900:
                print(f"      ⚠️ PARTIAL EXTRACTION (~50%)")
            else:
                print(f"      ❌ INCOMPLETE EXTRACTION")
        
        if not latest_session:
            print("❌ No extraction sessions found")
            return False
        
        # 2. DETAILED ANALYSIS OF LATEST SESSION
        print(f"\n2. 📋 DETAILED ANALYSIS OF LATEST SESSION:")
        print(f"   Session: {latest_session}")
        
        # Check period breakdown
        cursor.execute("""
            SELECT period_type, 
                   COUNT(DISTINCT employee_id) as employees,
                   COUNT(*) as records
            FROM extracted_data
            WHERE session_id = ?
            GROUP BY period_type
        """, (latest_session,))
        
        periods = cursor.fetchall()
        
        total_employees = 0
        for period, employees, records in periods:
            print(f"   📊 {period}: {employees} employees, {records} records")
            total_employees += employees
        
        print(f"   📊 TOTAL: {total_employees} employees")
        
        # 3. CHECK DEPARTMENT DATA AVAILABILITY
        print(f"\n3. 📋 DEPARTMENT DATA ANALYSIS:")
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as employees_with_dept
            FROM extracted_data
            WHERE session_id = ? 
            AND item_label = 'DEPARTMENT'
            AND item_value IS NOT NULL 
            AND item_value != ''
            AND item_value != 'None'
        """, (latest_session,))
        
        dept_employees = cursor.fetchone()[0]
        print(f"   📊 Employees with department data: {dept_employees}")
        
        if dept_employees > 0:
            # Sample department values
            cursor.execute("""
                SELECT DISTINCT item_value, COUNT(*) as count
                FROM extracted_data
                WHERE session_id = ? 
                AND item_label = 'DEPARTMENT'
                AND item_value IS NOT NULL 
                AND item_value != ''
                AND item_value != 'None'
                GROUP BY item_value
                ORDER BY count DESC
                LIMIT 10
            """, (latest_session,))
            
            dept_samples = cursor.fetchall()
            print(f"   📊 Sample departments:")
            for dept, count in dept_samples:
                print(f"      {dept}: {count} employees")
        
        # 4. CHECK EMPLOYEES TABLE
        print(f"\n4. 📋 EMPLOYEES TABLE ANALYSIS:")
        
        cursor.execute("""
            SELECT session_id, 
                   COUNT(*) as total_employees,
                   COUNT(CASE WHEN department IS NOT NULL AND department != '' AND department != 'None' THEN 1 END) as with_dept,
                   MAX(created_at) as latest_time
            FROM employees
            GROUP BY session_id
            ORDER BY latest_time DESC
            LIMIT 5
        """)
        
        employee_sessions = cursor.fetchall()
        
        for session_id, total, with_dept, latest_time in employee_sessions:
            print(f"   📊 {session_id}: {total} employees, {with_dept} with departments ({latest_time})")
        
        # 5. CHECK SPECIFIC MISSING EMPLOYEES
        print(f"\n5. 📋 CHECKING PREVIOUSLY MISSING EMPLOYEES:")
        
        missing_employees = ['PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617']
        
        for emp_id in missing_employees:
            # Check in extracted_data
            cursor.execute("""
                SELECT COUNT(*) as record_count,
                       COUNT(CASE WHEN item_label = 'DEPARTMENT' THEN 1 END) as dept_records
                FROM extracted_data
                WHERE session_id = ? AND employee_id = ?
            """, (latest_session, emp_id))
            
            result = cursor.fetchone()
            if result:
                record_count, dept_records = result
                if record_count > 0:
                    print(f"   📊 {emp_id}: {record_count} records, {dept_records} department records")
                    
                    if dept_records > 0:
                        # Get the department value
                        cursor.execute("""
                            SELECT item_value
                            FROM extracted_data
                            WHERE session_id = ? AND employee_id = ? AND item_label = 'DEPARTMENT'
                            LIMIT 1
                        """, (latest_session, emp_id))
                        
                        dept_result = cursor.fetchone()
                        if dept_result:
                            print(f"      ✅ Department: {dept_result[0]}")
                    else:
                        print(f"      ❌ No department data")
                else:
                    print(f"   ❌ {emp_id}: Not found in latest session")
            
            # Check in employees table
            cursor.execute("""
                SELECT department
                FROM employees
                WHERE employee_id = ?
                AND department IS NOT NULL 
                AND department != '' 
                AND department != 'None'
                ORDER BY created_at DESC
                LIMIT 1
            """, (emp_id,))
            
            emp_result = cursor.fetchone()
            if emp_result:
                print(f"      ✅ Found in employees table: {emp_result[0]}")
        
        # 6. FINAL ASSESSMENT
        print(f"\n6. 🎯 FINAL ASSESSMENT:")
        print("=" * 50)
        
        if total_employees >= 5800:
            print(f"✅ EXCELLENT: {total_employees} employees extracted (expected ~5919)")
            
            if dept_employees >= 5500:
                print(f"✅ EXCELLENT: {dept_employees} employees have department data")
                print(f"🎯 SUCCESS: Complete extraction with department data")
            else:
                print(f"⚠️ WARNING: Only {dept_employees} employees have department data")
                print(f"🎯 ISSUE: Extraction complete but department data incomplete")
        else:
            print(f"❌ INCOMPLETE: Only {total_employees} employees extracted (expected ~5919)")
            print(f"🎯 ISSUE: Extraction still incomplete despite fixes")
        
        conn.close()
        return latest_session
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    session = check_database_extraction()
    if session:
        print(f"\n🎯 LATEST SESSION: {session}")
    sys.exit(0)
