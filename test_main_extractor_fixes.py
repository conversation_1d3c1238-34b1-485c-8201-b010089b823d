#!/usr/bin/env python3
"""
Test Main Extractor Fixes
Test that all fixes are implemented in the main extractor file
"""

import os
import sys

def test_main_extractor_fixes():
    """Test the main extractor fixes"""
    print("🔍 TESTING MAIN EXTRACTOR FIXES")
    print("=" * 60)
    
    try:
        from perfect_section_aware_extractor import PerfectSectionAwareExtractor
        
        # Initialize the extractor
        extractor = PerfectSectionAwareExtractor(debug=True)
        
        print("✅ Main extractor initialized successfully")
        
        # Test both helper functions exist
        print(f"\n📊 CHECKING HELPER FUNCTIONS:")
        
        if hasattr(extractor, '_is_mostly_alphabetic'):
            print(f"   ✅ _is_mostly_alphabetic method exists")
        else:
            print(f"   ❌ _is_mostly_alphabetic method missing")
            return False
        
        if hasattr(extractor, '_is_likely_person_name'):
            print(f"   ✅ _is_likely_person_name method exists")
        else:
            print(f"   ❌ _is_likely_person_name method missing")
            return False
        
        # Test department names with special characters
        print(f"\n📊 TESTING DEPARTMENT NAME VALIDATION:")
        
        dept_test_cases = [
            "KASOA AREA-WID/PENSIONS",
            "NSAWAM AREA-WID/PENSIONS", 
            "HUMAN RESOURCE",
            "PENTMEDIA STAFF"
        ]
        
        for dept_name in dept_test_cases:
            result = extractor._is_mostly_alphabetic(dept_name)
            status = "✅" if result else "❌"
            print(f"   {status} {dept_name}: {result}")
        
        # Test person name validation
        print(f"\n📊 TESTING PERSON NAME VALIDATION:")
        
        name_test_cases = [
            ("NYARKO  O.   F.", True),           # Should be True
            ("JOHN SMITH", True),               # Should be True
            ("Ghana Card ID", False),           # Should be False
            ("RETIRED MINISTERS", False),       # Should be False
            ("PRINCIPAL OFFICER", False),       # Should be False
        ]
        
        all_passed = True
        for name, expected in name_test_cases:
            result = extractor._is_likely_person_name(name)
            status = "✅" if result == expected else "❌"
            if result != expected:
                all_passed = False
            print(f"   {status} {name}: {result} (expected: {expected})")
        
        print(f"\n🎯 IMPLEMENTATION STATUS:")
        print("=" * 50)
        
        if all_passed:
            print(f"✅ ALL FIXES PROPERLY IMPLEMENTED")
            print(f"   ✅ Department extraction with special characters")
            print(f"   ✅ Person name validation and filtering")
            print(f"   ✅ Field misalignment prevention")
            print(f"\n🚀 READY FOR EXTRACTION:")
            print(f"   - PW0085 should get proper department and name")
            print(f"   - PW0101 should get proper department and name (not 'Ghana Card ID')")
            print(f"   - All employees should have correct field alignment")
        else:
            print(f"❌ SOME FIXES NOT WORKING CORRECTLY")
            print(f"   🔧 Need to debug the failing test cases")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_main_extractor_fixes()
    if success:
        print(f"\n✅ MAIN EXTRACTOR FIXES CONFIRMED")
    else:
        print(f"\n❌ MAIN EXTRACTOR FIXES INCOMPLETE")
    sys.exit(0 if success else 1)
