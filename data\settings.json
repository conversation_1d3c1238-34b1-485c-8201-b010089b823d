{"performance": {"enableWorkerThreads": true, "workerTimeout": 30, "batchThreshold": 1000, "batchSize": 50, "enableRealTimeUpdates": true}, "extraction": {"useOnlyPerfectExtractor": true, "enableAutoLearning": true, "debugMode": false}, "ui": {"showProgressDetails": true, "enableNotifications": true, "autoSaveReports": true}, "database": {"enableUnifiedDatabase": true, "autoBackup": true, "performanceMaintenance": true}}