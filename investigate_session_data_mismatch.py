#!/usr/bin/env python3
"""
Investigate Session Data Mismatch
Why does the session have tracker data but no employee data with departments?
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_session_mismatch():
    """Investigate why session has tracker data but no employee data"""
    print("🔍 INVESTIGATING SESSION DATA MISMATCH")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # The problematic session
        session_id = "audit_session_1751122785_5479febb"
        print(f"📊 Investigating session: {session_id}")
        
        # 1. Check what data exists for this session
        print(f"\n1. 📋 DATA INVENTORY FOR THIS SESSION:")
        
        tables_to_check = [
            'employees', 'extracted_data', 'tracker_results', 
            'audit_sessions', 'payroll_sessions'
        ]
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE session_id = ?", (session_id,))
                    count = cursor.fetchone()[0]
                    print(f"   📊 {table}: {count} records")
                    
                    if count > 0 and table == 'tracker_results':
                        # Sample tracker data
                        cursor.execute(f"SELECT employee_id, employee_name FROM {table} WHERE session_id = ? LIMIT 3", (session_id,))
                        samples = cursor.fetchall()
                        for emp_id, emp_name in samples:
                            print(f"      📋 {emp_id}: {emp_name}")
                            
                else:
                    print(f"   ❌ {table}: Table doesn't exist")
            except Exception as e:
                print(f"   ❌ {table}: Error - {e}")
        
        # 2. Check if there's a different session that should be used
        print(f"\n2. 📋 FINDING THE CORRECT SESSION FOR THESE EMPLOYEES:")
        
        # Get employee IDs from tracker_results
        cursor.execute("SELECT DISTINCT employee_id FROM tracker_results WHERE session_id = ? LIMIT 10", (session_id,))
        tracker_employees = [row[0] for row in cursor.fetchall()]
        
        if tracker_employees:
            print(f"   📊 Sample employee IDs from tracker: {tracker_employees[:5]}")
            
            # Find which sessions have these employees with department data
            for emp_id in tracker_employees[:3]:  # Check first 3
                cursor.execute("""
                    SELECT session_id, department 
                    FROM employees 
                    WHERE employee_id = ? 
                    AND department IS NOT NULL AND department != ''
                    ORDER BY created_at DESC
                """, (emp_id,))
                
                employee_sessions = cursor.fetchall()
                if employee_sessions:
                    print(f"   ✅ {emp_id} found in sessions:")
                    for sess_id, dept in employee_sessions:
                        print(f"      📋 {sess_id}: {dept}")
                else:
                    print(f"   ❌ {emp_id}: No employee record with department found")
        
        # 3. Check if the session mapping is wrong
        print(f"\n3. 📋 CHECKING SESSION RELATIONSHIPS:")
        
        # Check audit_sessions table
        try:
            cursor.execute("SELECT * FROM audit_sessions WHERE session_id = ?", (session_id,))
            audit_session = cursor.fetchone()
            if audit_session:
                print(f"   ✅ Audit session exists: {audit_session}")
            else:
                print(f"   ❌ No audit session record found")
                
                # Check what audit sessions do exist
                cursor.execute("SELECT session_id, created_at FROM audit_sessions ORDER BY created_at DESC LIMIT 5")
                recent_sessions = cursor.fetchall()
                print(f"   📋 Recent audit sessions:")
                for sess_id, created_at in recent_sessions:
                    print(f"      {sess_id}: {created_at}")
        except Exception as e:
            print(f"   ❌ Error checking audit_sessions: {e}")
        
        # 4. Find the most recent session with employee data
        print(f"\n4. 📋 FINDING MOST RECENT SESSION WITH EMPLOYEE DATA:")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as emp_count,
                   COUNT(CASE WHEN department IS NOT NULL AND department != '' THEN 1 END) as with_dept,
                   MAX(created_at) as latest
            FROM employees 
            GROUP BY session_id 
            HAVING with_dept > 0
            ORDER BY latest DESC 
            LIMIT 5
        """)
        
        recent_employee_sessions = cursor.fetchall()
        if recent_employee_sessions:
            print(f"   📊 Recent sessions with employee department data:")
            for sess_id, emp_count, with_dept, latest in recent_employee_sessions:
                print(f"      {sess_id}: {with_dept}/{emp_count} employees with departments ({latest})")
                
                # Check if any of our tracker employees are in this session
                if tracker_employees:
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM employees 
                        WHERE session_id = ? AND employee_id IN ({','.join(['?' for _ in tracker_employees])})
                    """, [sess_id] + tracker_employees)
                    
                    matching_count = cursor.fetchone()[0]
                    if matching_count > 0:
                        print(f"         🎯 MATCH: {matching_count} tracker employees found in this session!")
                        
                        # This might be the correct session to use
                        cursor.execute("""
                            SELECT employee_id, department 
                            FROM employees 
                            WHERE session_id = ? AND employee_id IN ({})
                            LIMIT 5
                        """.format(','.join(['?' for _ in tracker_employees[:5]])), [sess_id] + tracker_employees[:5])
                        
                        matches = cursor.fetchall()
                        for emp_id, dept in matches:
                            print(f"            {emp_id}: {dept}")
        
        conn.close()
        
        print(f"\n🎯 INVESTIGATION COMPLETE")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        return False

def suggest_fix():
    """Suggest how to fix the department lookup"""
    print("\n💡 RECOMMENDED FIX:")
    print("=" * 30)
    print("1. The tracker session and employee session are DIFFERENT")
    print("2. Need to find the correct employee session that contains the same employees")
    print("3. Update department lookup to use the correct session mapping")
    print("4. OR fix the session creation process to ensure consistency")

if __name__ == "__main__":
    success = investigate_session_mismatch()
    if success:
        suggest_fix()
    sys.exit(0 if success else 1)
