#!/usr/bin/env python3
"""
Add Department Column
Add department column to tracker_results table and update existing records
"""

import os
import sys
import sqlite3

def get_database_path():
    """Get the database path"""
    return r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"

def add_department_column():
    """Add department column to tracker_results table"""
    print("🔧 ADDING DEPARTMENT COLUMN TO TRACKER_RESULTS")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK IF COLUMN ALREADY EXISTS
        print("1. 📋 CHECKING CURRENT SCHEMA:")
        
        cursor.execute("PRAGMA table_info(tracker_results)")
        columns = cursor.fetchall()
        
        has_department = any(col[1].lower() == 'department' for col in columns)
        
        if has_department:
            print("   ✅ Department column already exists")
        else:
            print("   ❌ Department column missing")
            
            # 2. ADD DEPARTMENT COLUMN
            print("\n2. 🔧 ADDING DEPARTMENT COLUMN:")
            
            cursor.execute("""
                ALTER TABLE tracker_results 
                ADD COLUMN department TEXT
            """)
            
            print("   ✅ Department column added successfully")
        
        # 3. UPDATE EXISTING RECORDS WITH DEPARTMENT DATA
        print(f"\n3. 🔧 UPDATING EXISTING RECORDS WITH DEPARTMENT DATA:")
        
        # Get all unique employee IDs from tracker_results
        cursor.execute("""
            SELECT DISTINCT employee_id 
            FROM tracker_results 
            WHERE department IS NULL OR department = ''
        """)
        
        employees_to_update = [row[0] for row in cursor.fetchall()]
        
        print(f"   📊 Found {len(employees_to_update)} employees needing department updates")
        
        updated_count = 0
        
        for employee_id in employees_to_update:
            # Get department from extracted_data
            cursor.execute("""
                SELECT item_value FROM extracted_data
                WHERE employee_id = ?
                AND item_label = 'DEPARTMENT'
                AND item_value IS NOT NULL 
                AND item_value != '' 
                AND item_value != 'None'
                ORDER BY created_at DESC 
                LIMIT 1
            """, (employee_id,))
            
            dept_result = cursor.fetchone()
            
            if dept_result and dept_result[0]:
                department = str(dept_result[0]).strip()
                
                # Update tracker_results with department
                cursor.execute("""
                    UPDATE tracker_results 
                    SET department = ?
                    WHERE employee_id = ?
                    AND (department IS NULL OR department = '')
                """, (department, employee_id))
                
                updated_count += 1
                
                if updated_count <= 5:  # Show first 5 updates
                    print(f"      ✅ {employee_id}: {department}")
        
        print(f"   📊 Updated {updated_count} employee records with departments")
        
        # 4. VERIFY UPDATES
        print(f"\n4. 📋 VERIFYING UPDATES:")
        
        cursor.execute("""
            SELECT COUNT(*) FROM tracker_results
            WHERE department IS NOT NULL AND department != ''
        """)
        
        records_with_dept = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results")
        total_records = cursor.fetchone()[0]
        
        coverage = (records_with_dept / total_records) * 100 if total_records > 0 else 0
        
        print(f"   📊 Total records: {total_records}")
        print(f"   📊 Records with departments: {records_with_dept}")
        print(f"   📊 Department coverage: {coverage:.1f}%")
        
        # 5. CHECK OUR TARGET EMPLOYEES
        print(f"\n5. 📋 CHECKING TARGET EMPLOYEES:")
        
        target_employees = ['COP2894', 'COP3195', 'COP3617']
        
        for emp_id in target_employees:
            cursor.execute("""
                SELECT department FROM tracker_results
                WHERE employee_id = ?
                AND department IS NOT NULL AND department != ''
                LIMIT 1
            """, (emp_id,))
            
            dept_result = cursor.fetchone()
            
            if dept_result:
                print(f"   ✅ {emp_id}: {dept_result[0]}")
            else:
                print(f"   ❌ {emp_id}: No department found")
        
        # 6. COMMIT CHANGES
        conn.commit()
        print(f"\n6. ✅ CHANGES COMMITTED TO DATABASE")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to add department column: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = add_department_column()
    if success:
        print(f"\n✅ DEPARTMENT COLUMN SUCCESSFULLY ADDED")
    else:
        print(f"\n❌ FAILED TO ADD DEPARTMENT COLUMN")
    sys.exit(0 if success else 1)
