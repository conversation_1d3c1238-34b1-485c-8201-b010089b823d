#!/usr/bin/env python3
"""
Check Tracker Schema
Check if tracker_results table needs department column
"""

import os
import sys
import sqlite3

def get_database_path():
    """Get the database path"""
    return r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"

def check_tracker_schema():
    """Check tracker_results table schema"""
    print("🔍 CHECKING TRACKER_RESULTS TABLE SCHEMA")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK CURRENT TRACKER_RESULTS SCHEMA
        print("1. 📋 CURRENT TRACKER_RESULTS SCHEMA:")
        
        cursor.execute("PRAGMA table_info(tracker_results)")
        columns = cursor.fetchall()
        
        has_department = False
        print("   📊 Current columns:")
        for col_id, col_name, col_type, not_null, default_val, primary_key in columns:
            pk_marker = " (PK)" if primary_key else ""
            null_marker = " NOT NULL" if not_null else ""
            default_marker = f" DEFAULT {default_val}" if default_val else ""
            print(f"      {col_name}: {col_type}{pk_marker}{null_marker}{default_marker}")
            
            if col_name.lower() == 'department':
                has_department = True
        
        print(f"\n   📊 Has department column: {'✅ YES' if has_department else '❌ NO'}")
        
        # 2. CHECK SAMPLE DATA
        print(f"\n2. 📋 SAMPLE TRACKER_RESULTS DATA:")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results")
        total_records = cursor.fetchone()[0]
        
        print(f"   📊 Total records: {total_records}")
        
        if total_records > 0:
            cursor.execute("""
                SELECT * FROM tracker_results 
                ORDER BY created_at DESC 
                LIMIT 3
            """)
            
            sample_data = cursor.fetchall()
            
            print(f"   📊 Sample records:")
            for i, record in enumerate(sample_data):
                print(f"      Record {i+1}: {str(record)[:100]}...")
        
        # 3. CHECK BANK ADVISER SPECIFIC TABLES
        print(f"\n3. 📋 BANK ADVISER SPECIFIC TABLES:")
        
        bank_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table_name in bank_tables:
            print(f"\n   📊 {table_name.upper()} TABLE:")
            
            cursor.execute(f"PRAGMA table_info({table_name})")
            table_columns = cursor.fetchall()
            
            table_has_dept = False
            for col_id, col_name, col_type, not_null, default_val, primary_key in table_columns:
                if col_name.lower() == 'department':
                    table_has_dept = True
                    print(f"      ✅ {col_name}: {col_type}")
                elif col_name in ['employee_id', 'employee_name']:
                    print(f"      📋 {col_name}: {col_type}")
            
            if not table_has_dept:
                print(f"      ❌ No department column")
            
            # Check sample data
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"      📊 Records: {count}")
            
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
                sample = cursor.fetchone()
                print(f"      📋 Sample: {str(sample)[:80]}...")
        
        # 4. CHECK IF DEPARTMENT IS NEEDED
        print(f"\n4. 🎯 DEPARTMENT COLUMN ANALYSIS:")
        print("=" * 50)
        
        if has_department:
            print(f"✅ tracker_results already has department column")
        else:
            print(f"❌ tracker_results missing department column")
            print(f"   🔧 RECOMMENDATION: ADD department column")
            print(f"   📋 SQL to add column:")
            print(f"      ALTER TABLE tracker_results ADD COLUMN department TEXT;")
        
        # Check if bank adviser tables need department
        need_dept_tables = []
        for table_name in bank_tables:
            cursor.execute(f"PRAGMA table_info({table_name})")
            table_columns = cursor.fetchall()
            
            table_has_dept = any(col[1].lower() == 'department' for col in table_columns)
            if not table_has_dept:
                need_dept_tables.append(table_name)
        
        if need_dept_tables:
            print(f"\n❌ Bank adviser tables missing department:")
            for table_name in need_dept_tables:
                print(f"   📋 {table_name}")
                print(f"      SQL: ALTER TABLE {table_name} ADD COLUMN department TEXT;")
        else:
            print(f"\n✅ All bank adviser tables have department column")
        
        # 5. SHOW CURRENT POPULATION PROCESS
        print(f"\n5. 📋 CURRENT POPULATION PROCESS:")
        
        # Check how the tracker population works
        cursor.execute("""
            SELECT employee_id, employee_name, tracker_type, item_label, item_value
            FROM tracker_results
            WHERE employee_id IN ('COP2894', 'COP3195', 'COP3617')
            LIMIT 5
        """)
        
        target_records = cursor.fetchall()
        
        if target_records:
            print(f"   📊 Target employees in tracker_results:")
            for emp_id, name, tracker_type, label, value in target_records:
                print(f"      {emp_id}: {name} - {tracker_type} - {label}")
        else:
            print(f"   ❌ Target employees not in tracker_results")
        
        conn.close()
        return not has_department or len(need_dept_tables) > 0
        
    except Exception as e:
        print(f"❌ Schema check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    needs_update = check_tracker_schema()
    if needs_update:
        print(f"\n🔧 SCHEMA UPDATE NEEDED")
    else:
        print(f"\n✅ SCHEMA IS CORRECT")
    sys.exit(0)
