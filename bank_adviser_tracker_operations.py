#!/usr/bin/env python3
"""
Bank Adviser Tracker Operations
Handles tracker table operations: clear table, delete records, duplicate checker
"""

import sys
import json
import os
import time

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main entry point for tracker operations"""
    
    if len(sys.argv) < 2:
        print(json.dumps({
            'success': False,
            'error': 'Command required. Available: clear_table, delete_records, check_duplicates'
        }))
        return
    
    command = sys.argv[1]
    
    try:
        from core.bank_adviser_tracker_manager import BankAdviserTrackerManager
        
        manager = BankAdviserTrackerManager(debug=False)
        
        if command == 'clear_table':
            if len(sys.argv) < 3:
                result = {'success': False, 'error': 'Category required for clear_table'}
            else:
                category = sys.argv[2]
                result = manager.clear_table(category)
        
        elif command == 'delete_records':
            if len(sys.argv) < 4:
                result = {'success': False, 'error': 'Category and record IDs required for delete_records'}
            else:
                category = sys.argv[2]
                record_ids = [int(id_str) for id_str in sys.argv[3:]]
                result = manager.delete_records(category, record_ids)
        
        elif command == 'check_duplicates':
            year = int(sys.argv[2]) if len(sys.argv) > 2 else None
            from core.duplicate_checker import DuplicateChecker
            checker = DuplicateChecker(debug=False)
            result = checker.scan_all_tables_for_duplicates(year)
        
        elif command == 'get_tracker_data':
            if len(sys.argv) < 3:
                result = {'success': False, 'error': 'Category required for get_tracker_data'}
            else:
                category = sys.argv[2]
                # Parse filters if provided
                filters = {}
                if len(sys.argv) > 3:
                    try:
                        filters = json.loads(sys.argv[3])
                    except:
                        pass
                result = manager.get_tracker_data(category, filters)

        elif command == 'populate_tables':
            result = populate_bank_adviser_tables()
        
        else:
            result = {'success': False, 'error': f'Unknown command: {command}'}
        
        print(json.dumps(result))
    
    except Exception as e:
        print(json.dumps({
            'success': False,
            'error': str(e),
            'command': command
        }))

def get_employee_department(cursor, employee_id, session_id):
    """
    Get employee department from the employees table or extracted_data.

    Args:
        cursor: Database cursor
        employee_id: Employee ID to lookup
        session_id: Current session ID

    Returns:
        str: Employee department or fallback value
    """
    try:
        # First try to get department from employees table for current session
        cursor.execute("""
            SELECT department FROM employees
            WHERE employee_id = ? AND session_id = ?
            AND department IS NOT NULL AND department != ''
            ORDER BY created_at DESC LIMIT 1
        """, (employee_id, session_id))

        result = cursor.fetchone()
        if result and result[0]:
            dept = str(result[0]).strip()
            if dept not in ['None', 'NULL', '', 'UNKNOWN']:
                return dept

        # CRITICAL FIX: If not found in current session, try ANY session with this employee
        cursor.execute("""
            SELECT department FROM employees
            WHERE employee_id = ?
            AND department IS NOT NULL AND department != ''
            ORDER BY created_at DESC LIMIT 1
        """, (employee_id,))

        result = cursor.fetchone()
        if result and result[0]:
            dept = str(result[0]).strip()
            if dept not in ['None', 'NULL', '', 'UNKNOWN']:
                return dept

        # Fallback: Try to get from extracted_data PERSONAL DETAILS section
        cursor.execute("""
            SELECT item_value FROM extracted_data
            WHERE employee_id = ? AND session_id = ?
            AND section_name = 'PERSONAL DETAILS'
            AND item_label IN ('DEPARTMENT', 'DEPT', 'SECTION', 'DIVISION')
            AND item_value IS NOT NULL AND item_value != ''
            ORDER BY
                CASE item_label
                    WHEN 'DEPARTMENT' THEN 1
                    WHEN 'DEPT' THEN 2
                    WHEN 'SECTION' THEN 3
                    WHEN 'DIVISION' THEN 4
                END
            LIMIT 1
        """, (employee_id, session_id))

        result = cursor.fetchone()
        if result and result[0]:
            dept = str(result[0]).strip()
            print(f"DEBUG: Found department in extracted_data: '{dept}'", file=sys.stderr)
            if dept not in ['None', 'NULL', '', 'UNKNOWN']:
                return dept
        else:
            print(f"DEBUG: No department found in extracted_data for {employee_id}", file=sys.stderr)

        # ENHANCED FALLBACK: Try broader search in extracted_data
        print(f"DEBUG: Trying broader department search for {employee_id}", file=sys.stderr)
        cursor.execute("""
            SELECT item_value FROM extracted_data
            WHERE employee_id = ? AND session_id = ?
            AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%'
                 OR item_label LIKE '%SECTION%' OR item_label LIKE '%DIVISION%')
            AND item_value IS NOT NULL AND item_value != ''
            LIMIT 1
        """, (employee_id, session_id))

        result = cursor.fetchone()
        if result and result[0]:
            dept = str(result[0]).strip()
            print(f"DEBUG: Found department in broader search: '{dept}'", file=sys.stderr)
            if dept not in ['None', 'NULL', '', 'UNKNOWN']:
                return dept

        # SMART FALLBACK: Infer department from employee ID patterns
        print(f"DEBUG: Attempting smart department inference for {employee_id}", file=sys.stderr)

        # Common employee ID patterns and their likely departments
        id_patterns = {
            'COP': 'POLICE',
            'ADM': 'ADMINISTRATION',
            'FIN': 'FINANCE',
            'HR': 'HUMAN RESOURCES',
            'IT': 'INFORMATION TECHNOLOGY',
            'OPS': 'OPERATIONS',
            'SEC': 'SECURITY',
            'MED': 'MEDICAL',
            'ENG': 'ENGINEERING',
            'LOG': 'LOGISTICS'
        }

        # Try to match employee ID prefix
        employee_id_upper = str(employee_id).upper()
        for prefix, department in id_patterns.items():
            if employee_id_upper.startswith(prefix):
                print(f"DEBUG: Inferred department '{department}' from ID pattern '{prefix}'", file=sys.stderr)
                return department

        # If no pattern matches, try to extract from any available data
        cursor.execute("""
            SELECT DISTINCT section_name FROM extracted_data
            WHERE employee_id = ? AND session_id = ?
            AND section_name IS NOT NULL AND section_name != ''
            LIMIT 1
        """, (employee_id, session_id))

        section_result = cursor.fetchone()
        if section_result and section_result[0]:
            section = str(section_result[0]).strip().upper()
            if section not in ['PERSONAL DETAILS', 'ALLOWANCES', 'DEDUCTIONS']:
                print(f"DEBUG: Using section name as department: '{section}'", file=sys.stderr)
                return section

        # Final fallback: Use a generic department based on ID type
        if employee_id_upper.startswith(('COP', 'POL')):
            return 'POLICE DEPARTMENT'
        elif employee_id_upper.startswith(('EMP', 'STF')):
            return 'GENERAL STAFF'
        elif employee_id_upper.isdigit():
            return 'NUMBERED STAFF'
        else:
            return 'UNASSIGNED DEPARTMENT'

    except Exception as e:
        print(f"Error getting department for {employee_id}: {e}", file=sys.stderr)
        return 'DEPARTMENT_LOOKUP_ERROR'

def populate_bank_adviser_tables():
    """Populate Bank Adviser tables from tracker results"""
    import sqlite3
    import sys
    from pathlib import Path

    try:
        # CRITICAL FIX: Use correct database path
        db_path = Path(__file__).parent / "data" / "templar_payroll_auditor.db"
        if not db_path.exists():
            return {'success': False, 'error': 'Database file not found'}

        # CRITICAL FIX: Use connection with timeout and retry logic to prevent database locking
        max_retries = 3
        retry_delay = 1.0

        for attempt in range(max_retries):
            try:
                # Use timeout to prevent indefinite locking
                conn = sqlite3.connect(str(db_path), timeout=30.0)
                conn.execute('PRAGMA journal_mode=WAL')  # Enable WAL mode for better concurrency
                conn.execute('PRAGMA busy_timeout=30000')  # 30 second busy timeout
                cursor = conn.cursor()
                break
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                    print(f"Database locked, retrying in {retry_delay} seconds... (attempt {attempt + 1}/{max_retries})", file=sys.stderr)
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    return {'success': False, 'error': f'Database connection failed after {max_retries} attempts: {str(e)}'}
        else:
            return {'success': False, 'error': 'Failed to connect to database after maximum retries'}

        # CRITICAL FIX: Get session with actual tracker data
        cursor.execute("""
            SELECT session_id, COUNT(*) as tracker_count
            FROM tracker_results
            GROUP BY session_id
            ORDER BY tracker_count DESC, session_id DESC
            LIMIT 1
        """)

        result = cursor.fetchone()
        if not result:
            return {'success': False, 'error': 'No tracker data found in any session'}

        current_session = result[0]
        tracker_count = result[1]

        print(f"Using session {current_session} with {tracker_count} tracker items", file=sys.stderr)

        # CRITICAL FIX: Use transaction to ensure data consistency
        conn.execute('BEGIN TRANSACTION')

        try:
            # Clear existing data for this session
            tables_to_clear = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']

            for table in tables_to_clear:
                cursor.execute(f"DELETE FROM {table} WHERE source_session = ?", (current_session,))
                print(f"Cleared {cursor.rowcount} records from {table}", file=sys.stderr)

            # Populate in-house loans
            cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value
            FROM tracker_results
            WHERE session_id = ? AND tracker_type = 'IN_HOUSE_LOAN'
            """, (current_session,))

            in_house_data = cursor.fetchall()
            in_house_count = 0

            for row in in_house_data:
                try:
                    employee_id = row[0]
                    employee_name = row[1]
                    loan_type = row[2]  # item_label
                    if ' - ' in loan_type:
                        loan_type = loan_type.split(' - ')[0].strip()

                    # Get actual employee department
                    department = get_employee_department(cursor, employee_id, current_session)

                    cursor.execute("""
                        INSERT INTO in_house_loans
                        (employee_no, employee_name, department, loan_type, loan_amount,
                         period_month, period_year, period_acquired, source_session, remarks)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        employee_id, employee_name, department, loan_type, row[4] or 0,
                        '06', '2025', '2025-06', current_session, 'Monitoring'
                    ))
                    in_house_count += 1
                except Exception as e:
                    print(f"Error inserting in-house loan for {row[0]}: {e}", file=sys.stderr)

            # Populate external loans
            cursor.execute("""
                SELECT employee_id, employee_name, item_label, item_value, numeric_value
                FROM tracker_results
                WHERE session_id = ? AND tracker_type = 'EXTERNAL_LOAN'
            """, (current_session,))

            external_data = cursor.fetchall()
            external_count = 0

            for row in external_data:
                try:
                    employee_id = row[0]
                    employee_name = row[1]
                    loan_type = row[2]  # item_label
                    if ' - ' in loan_type:
                        loan_type = loan_type.split(' - ')[0].strip()

                    # Get actual employee department
                    department = get_employee_department(cursor, employee_id, current_session)

                    cursor.execute("""
                        INSERT INTO external_loans
                        (employee_no, employee_name, department, loan_type, loan_amount,
                         period_month, period_year, period_acquired, source_session)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        employee_id, employee_name, department, loan_type, row[4] or 0,
                        '06', '2025', '2025-06', current_session
                    ))
                    external_count += 1
                except Exception as e:
                    print(f"Error inserting external loan for {row[0]}: {e}", file=sys.stderr)

            # Populate motor vehicle maintenance
            cursor.execute("""
                SELECT employee_id, employee_name, item_label, item_value, numeric_value
                FROM tracker_results
                WHERE session_id = ? AND tracker_type = 'MOTOR_VEHICLE'
            """, (current_session,))

            motor_data = cursor.fetchall()
            motor_count = 0

            for row in motor_data:
                try:
                    employee_id = row[0]
                    employee_name = row[1]
                    allowance_type = row[2]
                    amount = row[4] or 0

                    # Get actual employee department
                    department = get_employee_department(cursor, employee_id, current_session)

                    cursor.execute("""
                        INSERT INTO motor_vehicle_maintenance
                        (employee_no, employee_name, department, allowance_type,
                         allowance_amount, payable_amount, maintenance_amount,
                         period_month, period_year, period_acquired,
                         source_session, remarks)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        employee_id, employee_name, department, allowance_type,
                        amount, amount, amount,
                        '06', '2025', '2025-06', current_session, 'Monitoring'
                    ))
                    motor_count += 1
                except Exception as e:
                    print(f"Error inserting motor vehicle maintenance for {row[0]}: {e}", file=sys.stderr)

            # CRITICAL FIX: Commit transaction only if all operations succeed
            conn.commit()
            total = in_house_count + external_count + motor_count

            print(f"Successfully populated {total} records across all tables", file=sys.stderr)

            return {
                'success': True,
                'in_house_loans': in_house_count,
                'external_loans': external_count,
                'motor_vehicles': motor_count,
                'total': total
            }

        except Exception as inner_e:
            # CRITICAL FIX: Rollback transaction on any error during population
            conn.rollback()
            print(f"Transaction rolled back due to error: {inner_e}", file=sys.stderr)
            raise inner_e

    except Exception as e:
        # CRITICAL FIX: Enhanced error reporting for debugging
        error_msg = f"Tracker population failed: {str(e)}"
        print(f"ERROR: {error_msg}", file=sys.stderr)
        return {'success': False, 'error': error_msg}
    finally:
        # CRITICAL FIX: Always close connection to prevent database locking
        if 'conn' in locals():
            try:
                conn.close()
            except:
                pass

if __name__ == '__main__':
    main()
