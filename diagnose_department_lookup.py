#!/usr/bin/env python3
"""
Diagnose Department Lookup Issues
Investigates why department lookup is failing and showing DEPARTMENT_NOT_FOUND
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def diagnose_department_data():
    """Diagnose department lookup issues"""
    print("🔍 DEPARTMENT LOOKUP DIAGNOSIS")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. Find the session with tracker data
        cursor.execute("""
            SELECT session_id, COUNT(*) as count 
            FROM tracker_results 
            GROUP BY session_id 
            ORDER BY count DESC 
            LIMIT 1
        """)
        
        session_result = cursor.fetchone()
        if not session_result:
            print("❌ No tracker data found")
            return False
        
        session_id, tracker_count = session_result
        print(f"📊 Using session: {session_id} ({tracker_count} tracker items)")
        
        # 2. Check employees table structure and data
        print(f"\n2. 📋 EMPLOYEES TABLE ANALYSIS:")
        
        # Check if employees table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='employees'")
        if not cursor.fetchone():
            print("❌ employees table does not exist")
        else:
            print("✅ employees table exists")
            
            # Check table structure
            cursor.execute("PRAGMA table_info(employees)")
            columns = cursor.fetchall()
            print("📋 Table structure:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # Check data in employees table for this session
            cursor.execute("SELECT COUNT(*) FROM employees WHERE session_id = ?", (session_id,))
            emp_count = cursor.fetchone()[0]
            print(f"📊 Employees in session {session_id}: {emp_count}")
            
            if emp_count > 0:
                # Sample some employee data
                cursor.execute("""
                    SELECT employee_id, department, created_at 
                    FROM employees 
                    WHERE session_id = ? 
                    LIMIT 5
                """, (session_id,))
                
                sample_employees = cursor.fetchall()
                print("📋 Sample employee data:")
                for emp in sample_employees:
                    print(f"   ID: {emp[0]}, Dept: {emp[1]}, Created: {emp[2]}")
        
        # 3. Check extracted_data table for department information
        print(f"\n3. 📋 EXTRACTED_DATA TABLE ANALYSIS:")
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='extracted_data'")
        if not cursor.fetchone():
            print("❌ extracted_data table does not exist")
        else:
            print("✅ extracted_data table exists")
            
            # Check for department-related data
            cursor.execute("""
                SELECT COUNT(*) FROM extracted_data 
                WHERE session_id = ? 
                AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%' 
                     OR item_label LIKE '%SECTION%' OR item_label LIKE '%DIVISION%')
            """, (session_id,))
            
            dept_items = cursor.fetchone()[0]
            print(f"📊 Department-related items in extracted_data: {dept_items}")
            
            if dept_items > 0:
                # Sample department data
                cursor.execute("""
                    SELECT employee_id, section_name, item_label, item_value 
                    FROM extracted_data 
                    WHERE session_id = ? 
                    AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%' 
                         OR item_label LIKE '%SECTION%' OR item_label LIKE '%DIVISION%')
                    LIMIT 10
                """, (session_id,))
                
                dept_data = cursor.fetchall()
                print("📋 Sample department data from extracted_data:")
                for data in dept_data:
                    print(f"   Employee: {data[0]}, Section: {data[1]}, Label: {data[2]}, Value: {data[3]}")
        
        # 4. Check tracker_results for employee IDs that need departments
        print(f"\n4. 📋 TRACKER RESULTS ANALYSIS:")
        
        cursor.execute("""
            SELECT DISTINCT employee_id, employee_name 
            FROM tracker_results 
            WHERE session_id = ? 
            LIMIT 10
        """, (session_id,))
        
        tracker_employees = cursor.fetchall()
        print(f"📊 Sample employees from tracker_results:")
        for emp in tracker_employees:
            print(f"   ID: {emp[0]}, Name: {emp[1]}")
        
        # 5. Test department lookup for a specific employee
        if tracker_employees:
            test_employee_id = tracker_employees[0][0]
            print(f"\n5. 🧪 TESTING DEPARTMENT LOOKUP FOR EMPLOYEE {test_employee_id}:")
            
            # Test employees table lookup
            cursor.execute("""
                SELECT department FROM employees
                WHERE employee_id = ? AND session_id = ?
                AND department IS NOT NULL AND department != ''
                ORDER BY created_at DESC LIMIT 1
            """, (test_employee_id, session_id))
            
            emp_result = cursor.fetchone()
            if emp_result:
                print(f"✅ Found in employees table: {emp_result[0]}")
            else:
                print("❌ Not found in employees table")
            
            # Test extracted_data lookup
            cursor.execute("""
                SELECT item_value FROM extracted_data
                WHERE employee_id = ? AND session_id = ?
                AND section_name = 'PERSONAL DETAILS'
                AND item_label IN ('DEPARTMENT', 'DEPT', 'SECTION', 'DIVISION')
                AND item_value IS NOT NULL AND item_value != ''
                LIMIT 1
            """, (test_employee_id, session_id))
            
            ext_result = cursor.fetchone()
            if ext_result:
                print(f"✅ Found in extracted_data: {ext_result[0]}")
            else:
                print("❌ Not found in extracted_data PERSONAL DETAILS")
                
                # Try broader search
                cursor.execute("""
                    SELECT item_label, item_value FROM extracted_data
                    WHERE employee_id = ? AND session_id = ?
                    AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%' 
                         OR item_label LIKE '%SECTION%' OR item_label LIKE '%DIVISION%')
                    AND item_value IS NOT NULL AND item_value != ''
                """, (test_employee_id, session_id))
                
                broad_results = cursor.fetchall()
                if broad_results:
                    print("✅ Found in broader search:")
                    for label, value in broad_results:
                        print(f"   {label}: {value}")
                else:
                    print("❌ Not found in broader search either")
        
        conn.close()
        
        print(f"\n🎯 DIAGNOSIS COMPLETE")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Diagnosis failed: {e}")
        return False

def suggest_fixes():
    """Suggest potential fixes based on diagnosis"""
    print("\n💡 POTENTIAL FIXES:")
    print("=" * 30)
    print("1. Check if employee data extraction includes department information")
    print("2. Verify that department data is being stored in the correct table/column")
    print("3. Consider adding department mapping/inference logic")
    print("4. Check if department data is in a different section than 'PERSONAL DETAILS'")
    print("5. Add fallback to use a default department or derive from employee ID patterns")

if __name__ == "__main__":
    success = diagnose_department_data()
    if success:
        suggest_fixes()
    sys.exit(0 if success else 1)
