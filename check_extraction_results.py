#!/usr/bin/env python3
"""
Check Extraction Results
Check if the fixes worked for department and name extraction
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_extraction_results():
    """Check if extraction fixes worked"""
    print("🔍 CHECKING EXTRACTION RESULTS AFTER FIXES")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. FIND LATEST SESSION
        print("1. 📋 FINDING LATEST EXTRACTION SESSION:")
        
        cursor.execute("""
            SELECT session_id, COUNT(DISTINCT employee_id) as employees,
                   MAX(created_at) as latest_time
            FROM extracted_data
            GROUP BY session_id
            ORDER BY latest_time DESC
            LIMIT 3
        """)
        
        sessions = cursor.fetchall()
        
        if not sessions:
            print("   ❌ No extraction sessions found")
            return False
        
        latest_session = sessions[0][0]
        latest_count = sessions[0][1]
        
        print(f"   📊 Latest sessions:")
        for session_id, emp_count, time in sessions:
            print(f"      {session_id}: {emp_count} employees ({time})")
        
        print(f"\n   🎯 Using latest: {latest_session} ({latest_count} employees)")
        
        # 2. CHECK PW0085 AND PW0101 SPECIFICALLY
        print(f"\n2. 📋 CHECKING FIXED EMPLOYEES (PW0085, PW0101):")
        
        target_employees = ['PW0085', 'PW0101']
        
        for emp_id in target_employees:
            print(f"\n   🔍 {emp_id}:")
            
            # Check name
            cursor.execute("""
                SELECT item_value
                FROM extracted_data
                WHERE session_id = ? AND employee_id = ? AND item_label = 'EMPLOYEE NAME'
            """, (latest_session, emp_id))
            
            name_result = cursor.fetchone()
            if name_result:
                name = name_result[0]
                if 'GHANA CARD' in name.upper() or 'RETIRED MINISTERS' in name.upper():
                    print(f"      ❌ NAME: {name} (STILL CORRUPTED)")
                else:
                    print(f"      ✅ NAME: {name} (FIXED)")
            else:
                print(f"      ❌ NAME: Not found")
            
            # Check department
            cursor.execute("""
                SELECT item_value
                FROM extracted_data
                WHERE session_id = ? AND employee_id = ? AND item_label = 'DEPARTMENT'
            """, (latest_session, emp_id))
            
            dept_result = cursor.fetchone()
            if dept_result:
                dept = dept_result[0]
                if 'AREA' in dept.upper() and ('WID' in dept.upper() or 'PENSIONS' in dept.upper()):
                    print(f"      ✅ DEPARTMENT: {dept} (FIXED)")
                else:
                    print(f"      ⚠️ DEPARTMENT: {dept} (PARTIAL)")
            else:
                print(f"      ❌ DEPARTMENT: Not found")
        
        # 3. CHECK OVERALL DEPARTMENT EXTRACTION
        print(f"\n3. 📋 OVERALL DEPARTMENT EXTRACTION:")
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total_employees
            FROM extracted_data
            WHERE session_id = ?
        """, (latest_session,))
        
        total_employees = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as employees_with_dept
            FROM extracted_data
            WHERE session_id = ? AND item_label = 'DEPARTMENT'
        """, (latest_session,))
        
        employees_with_dept = cursor.fetchone()[0]
        
        dept_coverage = (employees_with_dept / total_employees) * 100 if total_employees > 0 else 0
        
        print(f"   📊 Total employees: {total_employees}")
        print(f"   📊 With departments: {employees_with_dept}")
        print(f"   📊 Department coverage: {dept_coverage:.1f}%")
        
        # 4. CHECK NAME EXTRACTION QUALITY
        print(f"\n4. 📋 NAME EXTRACTION QUALITY:")
        
        # Check for corrupted names
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as corrupted_names
            FROM extracted_data
            WHERE session_id = ? 
            AND item_label = 'EMPLOYEE NAME'
            AND (
                item_value LIKE '%GHANA CARD%' OR
                item_value LIKE '%RETIRED MINISTERS%' OR
                item_value LIKE '%OFFICER%' OR
                item_value LIKE '%MANAGER%'
            )
        """, (latest_session,))
        
        corrupted_names = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total_with_names
            FROM extracted_data
            WHERE session_id = ? AND item_label = 'EMPLOYEE NAME'
        """, (latest_session,))
        
        total_with_names = cursor.fetchone()[0]
        
        name_quality = ((total_with_names - corrupted_names) / total_with_names) * 100 if total_with_names > 0 else 0
        
        print(f"   📊 Total with names: {total_with_names}")
        print(f"   📊 Corrupted names: {corrupted_names}")
        print(f"   📊 Name quality: {name_quality:.1f}%")
        
        # 5. TEST TRACKER POPULATION
        print(f"\n5. 📋 TESTING TRACKER POPULATION:")
        
        print(f"   🚀 Running tracker population test...")
        
        # This will test if the department lookup now works
        import subprocess
        result = subprocess.run([
            sys.executable, "bank_adviser_tracker_operations.py", "populate_tables"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            output = result.stdout
            if "Successfully populated" in output:
                print(f"   ✅ Tracker population successful")
                
                # Check for department errors
                if "Error getting department" in output:
                    error_count = output.count("Error getting department")
                    print(f"   ⚠️ {error_count} employees still missing departments")
                else:
                    print(f"   ✅ All employees have departments!")
            else:
                print(f"   ❌ Tracker population failed")
        else:
            print(f"   ❌ Tracker test failed: {result.stderr}")
        
        # 6. FINAL ASSESSMENT
        print(f"\n6. 🎯 FINAL ASSESSMENT:")
        print("=" * 50)
        
        if dept_coverage >= 95 and name_quality >= 95:
            print(f"✅ EXCELLENT: Extraction fixes successful")
            print(f"   📊 Department coverage: {dept_coverage:.1f}%")
            print(f"   📊 Name quality: {name_quality:.1f}%")
            print(f"   🎯 Ready for production use")
        elif dept_coverage >= 85 and name_quality >= 85:
            print(f"✅ GOOD: Major improvements achieved")
            print(f"   📊 Department coverage: {dept_coverage:.1f}%")
            print(f"   📊 Name quality: {name_quality:.1f}%")
            print(f"   🔧 Minor issues remain")
        else:
            print(f"⚠️ PARTIAL: Some fixes worked, others need attention")
            print(f"   📊 Department coverage: {dept_coverage:.1f}%")
            print(f"   📊 Name quality: {name_quality:.1f}%")
            print(f"   🔧 Additional fixes needed")
        
        conn.close()
        return dept_coverage >= 85 and name_quality >= 85
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_extraction_results()
    sys.exit(0 if success else 1)
