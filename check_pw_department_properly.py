#!/usr/bin/env python3
"""
Check PW Department Data Properly
Check if PW employees actually have DEPARTMENT data that's being missed
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_pw_department_properly():
    """Check PW department data properly"""
    print("🔍 CHECKING PW DEPARTMENT DATA PROPERLY")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Latest session
        latest_session = "audit_session_1751148955_7321e46d"
        
        # 1. CHECK ALL PW EMPLOYEES FOR DEPARTMENT LABEL SPECIFICALLY
        print("1. 📋 CHECKING ALL PW EMPLOYEES FOR DEPARTMENT LABEL:")
        
        cursor.execute("""
            SELECT employee_id, item_value
            FROM extracted_data
            WHERE session_id = ? 
            AND employee_id LIKE 'PW%'
            AND item_label = 'DEPARTMENT'
            ORDER BY employee_id
            LIMIT 20
        """, (latest_session,))
        
        pw_departments = cursor.fetchall()
        
        if pw_departments:
            print(f"   ✅ FOUND {len(pw_departments)} PW EMPLOYEES WITH DEPARTMENT DATA:")
            for emp_id, dept in pw_departments:
                print(f"      {emp_id}: {dept}")
        else:
            print(f"   ❌ NO PW EMPLOYEES FOUND WITH DEPARTMENT LABEL")
        
        # 2. CHECK TOTAL COUNT OF PW EMPLOYEES WITH DEPARTMENT
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as pw_with_dept
            FROM extracted_data
            WHERE session_id = ? 
            AND employee_id LIKE 'PW%'
            AND item_label = 'DEPARTMENT'
        """, (latest_session,))
        
        pw_dept_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total_pw
            FROM extracted_data
            WHERE session_id = ? 
            AND employee_id LIKE 'PW%'
        """, (latest_session,))
        
        total_pw = cursor.fetchone()[0]
        
        print(f"\n   📊 PW DEPARTMENT SUMMARY:")
        print(f"      Total PW employees: {total_pw}")
        print(f"      PW with DEPARTMENT: {pw_dept_count}")
        print(f"      Coverage: {pw_dept_count/total_pw*100:.1f}%")
        
        # 3. CHECK SPECIFIC EMPLOYEES PW0085 AND PW0101 FOR DEPARTMENT
        print(f"\n2. 📋 CHECKING SPECIFIC EMPLOYEES FOR DEPARTMENT:")
        
        target_employees = ['PW0085', 'PW0101']
        
        for emp_id in target_employees:
            cursor.execute("""
                SELECT item_value
                FROM extracted_data
                WHERE session_id = ? 
                AND employee_id = ?
                AND item_label = 'DEPARTMENT'
            """, (latest_session, emp_id))
            
            dept_results = cursor.fetchall()
            
            if dept_results:
                print(f"   ✅ {emp_id} HAS DEPARTMENT DATA:")
                for dept in dept_results:
                    print(f"      DEPARTMENT: {dept[0]}")
            else:
                print(f"   ❌ {emp_id}: NO DEPARTMENT LABEL FOUND")
                
                # Check what labels they do have that might be department
                cursor.execute("""
                    SELECT item_label, item_value
                    FROM extracted_data
                    WHERE session_id = ? 
                    AND employee_id = ?
                    AND (
                        item_label LIKE '%DEPARTMENT%' OR 
                        item_label LIKE '%DEPT%' OR
                        item_value LIKE '%KASOA%' OR
                        item_value LIKE '%NSAWAM%' OR
                        item_value LIKE '%AREA%' OR
                        item_value LIKE '%WID%' OR
                        item_value LIKE '%PENSION%'
                    )
                """, (latest_session, emp_id))
                
                related_data = cursor.fetchall()
                if related_data:
                    print(f"      🔍 Related data found:")
                    for label, value in related_data:
                        print(f"         {label}: {value}")
        
        # 4. SEARCH FOR THE SPECIFIC DEPARTMENT NAME YOU MENTIONED
        print(f"\n3. 📋 SEARCHING FOR SPECIFIC DEPARTMENT NAME:")
        print(f"   Looking for 'KASOA AREA-WID/PENSIONS' pattern...")
        
        # Search for parts of the department name
        search_patterns = [
            'KASOA AREA-WID/PENSIONS',
            'KASOA AREA',
            'WID/PENSIONS',
            'WID',
            'PENSIONS'
        ]
        
        for pattern in search_patterns:
            cursor.execute("""
                SELECT employee_id, item_label, item_value
                FROM extracted_data
                WHERE session_id = ? 
                AND employee_id IN ('PW0085', 'PW0101')
                AND item_value LIKE ?
            """, (latest_session, f'%{pattern}%'))
            
            results = cursor.fetchall()
            if results:
                print(f"   🎯 Found pattern '{pattern}':")
                for emp_id, label, value in results:
                    print(f"      {emp_id}: {label} = {value}")
        
        # 5. CHECK IF DEPARTMENT DATA EXISTS BUT WITH DIFFERENT FORMATTING
        print(f"\n4. 📋 CHECKING FOR FORMATTING ISSUES:")
        
        # Look for any item_value that contains department-like information
        cursor.execute("""
            SELECT DISTINCT item_label, item_value
            FROM extracted_data
            WHERE session_id = ? 
            AND employee_id IN ('PW0085', 'PW0101')
            AND (
                item_value LIKE '%AREA%' OR
                item_value LIKE '%DEPARTMENT%' OR
                item_value LIKE '%MINISTERS%' OR
                item_value LIKE '%STAFF%' OR
                item_value LIKE '%WID%' OR
                item_value LIKE '%PENSION%'
            )
        """, (latest_session,))
        
        dept_like_values = cursor.fetchall()
        
        if dept_like_values:
            print(f"   🎯 Found department-like values:")
            for label, value in dept_like_values:
                print(f"      {label}: {value}")
        else:
            print(f"   ❌ No department-like values found")
        
        # 6. CHECK ALL SESSIONS FOR PW DEPARTMENT DATA
        print(f"\n5. 📋 CHECKING ALL SESSIONS FOR PW DEPARTMENT DATA:")
        
        cursor.execute("""
            SELECT session_id, COUNT(DISTINCT employee_id) as pw_with_dept
            FROM extracted_data
            WHERE employee_id LIKE 'PW%'
            AND item_label = 'DEPARTMENT'
            GROUP BY session_id
            ORDER BY pw_with_dept DESC
            LIMIT 5
        """, ())
        
        session_results = cursor.fetchall()
        
        if session_results:
            print(f"   📊 Sessions with PW department data:")
            for session_id, count in session_results:
                print(f"      {session_id}: {count} PW employees with departments")
                
                # Check if our target employees are in these sessions
                for emp_id in target_employees:
                    cursor.execute("""
                        SELECT item_value
                        FROM extracted_data
                        WHERE session_id = ? 
                        AND employee_id = ?
                        AND item_label = 'DEPARTMENT'
                    """, (session_id, emp_id))
                    
                    dept_result = cursor.fetchone()
                    if dept_result:
                        print(f"         ✅ {emp_id}: {dept_result[0]}")
        else:
            print(f"   ❌ NO sessions found with PW department data")
        
        # 7. FINAL TRUTH CHECK
        print(f"\n6. 🎯 FINAL TRUTH CHECK:")
        print("=" * 50)
        
        if pw_dept_count > 0:
            print(f"✅ CONFIRMED: {pw_dept_count} PW employees DO have DEPARTMENT data")
            
            if pw_dept_count == total_pw:
                print(f"✅ PERFECT: ALL PW employees have department data")
                print(f"🎯 Issue: Department lookup function has a bug")
            else:
                print(f"⚠️ PARTIAL: {total_pw - pw_dept_count} PW employees missing department data")
                print(f"🎯 Issue: Some PW employees have different extraction pattern")
        else:
            print(f"❌ CONFIRMED: NO PW employees have DEPARTMENT label in any session")
            print(f"🎯 Issue: PW employees use different field name or extraction failed")
        
        conn.close()
        return pw_dept_count > 0
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_pw_department_properly()
    sys.exit(0 if success else 1)
