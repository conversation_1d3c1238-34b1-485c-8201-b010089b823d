#!/usr/bin/env python3
"""
Find Missing Employees
Identify which employees from tracker are missing from employees table
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def find_missing_employees():
    """Find which employees are missing from employees table"""
    print("🔍 FINDING MISSING EMPLOYEES")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Get all tracker employees
        session_id = "audit_session_1751122785_5479febb"
        cursor.execute("SELECT DISTINCT employee_id, employee_name FROM tracker_results WHERE session_id = ?", (session_id,))
        tracker_employees = cursor.fetchall()
        
        print(f"📊 Total tracker employees: {len(tracker_employees)}")
        
        # Check which ones exist in employees table
        found_employees = []
        missing_employees = []
        
        for emp_id, emp_name in tracker_employees:
            cursor.execute("""
                SELECT COUNT(*) FROM employees 
                WHERE employee_id = ? 
                AND department IS NOT NULL AND department != ''
            """, (emp_id,))
            
            count = cursor.fetchone()[0]
            if count > 0:
                # Get the department
                cursor.execute("""
                    SELECT department FROM employees 
                    WHERE employee_id = ? 
                    AND department IS NOT NULL AND department != ''
                    ORDER BY created_at DESC LIMIT 1
                """, (emp_id,))
                dept = cursor.fetchone()[0]
                found_employees.append((emp_id, emp_name, dept))
            else:
                missing_employees.append((emp_id, emp_name))
        
        print(f"\n✅ FOUND EMPLOYEES ({len(found_employees)}):")
        for emp_id, emp_name, dept in found_employees:
            print(f"   {emp_id}: {emp_name} - {dept}")
        
        print(f"\n❌ MISSING EMPLOYEES ({len(missing_employees)}):")
        for emp_id, emp_name in missing_employees:
            print(f"   {emp_id}: {emp_name}")
        
        # Check if missing employees exist in any other table
        if missing_employees:
            print(f"\n🔍 CHECKING OTHER TABLES FOR MISSING EMPLOYEES:")
            
            tables_with_employee_id = [
                'final_adjusted_data', 'allowances_data', 'awards_data', 
                'extracted_data', 'sorted_payslips'
            ]
            
            for table in tables_with_employee_id:
                try:
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                    if cursor.fetchone():
                        for emp_id, emp_name in missing_employees[:5]:  # Check first 5
                            cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE employee_no = ? OR employee_id = ?", (emp_id, emp_id))
                            count = cursor.fetchone()[0]
                            if count > 0:
                                print(f"   ✅ {emp_id} found in {table}")
                                break
                except Exception as e:
                    pass
        
        conn.close()
        
        print(f"\n🎯 SUMMARY:")
        print(f"   Found: {len(found_employees)}/{len(tracker_employees)} employees")
        print(f"   Missing: {len(missing_employees)}/{len(tracker_employees)} employees")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        return False

if __name__ == "__main__":
    success = find_missing_employees()
    sys.exit(0 if success else 1)
