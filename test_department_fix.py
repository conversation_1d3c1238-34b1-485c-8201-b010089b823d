#!/usr/bin/env python3
"""
Test Department Fix
Test that the department column is properly populated in tracker_results
"""

import os
import sys
import sqlite3

def get_database_path():
    """Get the database path"""
    return r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"

def test_department_fix():
    """Test the department fix implementation"""
    print("🔍 TESTING DEPARTMENT FIX IMPLEMENTATION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. VERIFY TRACKER_RESULTS SCHEMA
        print("1. 📋 VERIFYING TRACKER_RESULTS SCHEMA:")
        
        cursor.execute("PRAGMA table_info(tracker_results)")
        columns = cursor.fetchall()
        
        has_department = False
        has_employee_name = False
        
        print("   📊 Current columns:")
        for col_id, col_name, col_type, not_null, default_val, primary_key in columns:
            print(f"      {col_name}: {col_type}")
            if col_name.lower() == 'department':
                has_department = True
            if col_name.lower() == 'employee_name':
                has_employee_name = True
        
        print(f"\n   📊 Schema status:")
        print(f"      Department column: {'✅ EXISTS' if has_department else '❌ MISSING'}")
        print(f"      Employee_name column: {'✅ EXISTS' if has_employee_name else '❌ MISSING'}")
        
        # 2. CHECK CURRENT DEPARTMENT COVERAGE
        print(f"\n2. 📋 CURRENT DEPARTMENT COVERAGE:")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results")
        total_records = cursor.fetchone()[0]
        
        if has_department:
            cursor.execute("""
                SELECT COUNT(*) FROM tracker_results
                WHERE department IS NOT NULL AND department != ''
            """)
            records_with_dept = cursor.fetchone()[0]
            
            coverage = (records_with_dept / total_records) * 100 if total_records > 0 else 0
            
            print(f"   📊 Total records: {total_records}")
            print(f"   📊 Records with departments: {records_with_dept}")
            print(f"   📊 Department coverage: {coverage:.1f}%")
        else:
            print(f"   ❌ Cannot check coverage - department column missing")
        
        # 3. CHECK SAMPLE RECORDS
        print(f"\n3. 📋 SAMPLE TRACKER RECORDS:")
        
        if has_department and has_employee_name:
            cursor.execute("""
                SELECT employee_id, employee_name, tracker_type, item_label, department
                FROM tracker_results
                WHERE department IS NOT NULL AND department != ''
                ORDER BY created_at DESC
                LIMIT 5
            """)
            
            sample_records = cursor.fetchall()
            
            if sample_records:
                print(f"   📊 Sample records with departments:")
                for emp_id, name, tracker_type, label, dept in sample_records:
                    print(f"      {emp_id}: {name} - {tracker_type} - {dept}")
            else:
                print(f"   ❌ No records with departments found")
        
        # 4. CHECK OUR TARGET EMPLOYEES
        print(f"\n4. 📋 CHECKING TARGET EMPLOYEES:")
        
        target_employees = ['COP2894', 'COP3195', 'COP3617']
        
        for emp_id in target_employees:
            cursor.execute("""
                SELECT employee_name, tracker_type, item_label, department
                FROM tracker_results
                WHERE employee_id = ?
                ORDER BY created_at DESC
                LIMIT 1
            """, (emp_id,))
            
            result = cursor.fetchone()
            
            if result:
                name, tracker_type, label, dept = result
                dept_status = "✅" if dept else "❌"
                print(f"   {dept_status} {emp_id}: {name} - {tracker_type}")
                if dept:
                    print(f"      📋 Department: {dept}")
                else:
                    print(f"      ❌ No department")
            else:
                print(f"   ❌ {emp_id}: Not found in tracker_results")
        
        # 5. TEST DEPARTMENT LOOKUP FUNCTION
        print(f"\n5. 📋 TESTING DEPARTMENT LOOKUP:")
        
        # Test the department lookup for our target employees
        for emp_id in target_employees:
            cursor.execute("""
                SELECT item_value FROM extracted_data
                WHERE employee_id = ?
                AND item_label = 'DEPARTMENT'
                AND item_value IS NOT NULL 
                AND item_value != '' 
                AND item_value != 'None'
                ORDER BY created_at DESC 
                LIMIT 1
            """, (emp_id,))
            
            dept_result = cursor.fetchone()
            
            if dept_result:
                print(f"   ✅ {emp_id}: Department in extracted_data = {dept_result[0]}")
            else:
                print(f"   ❌ {emp_id}: No department in extracted_data")
                
                # Check for SECTION as fallback
                cursor.execute("""
                    SELECT item_value FROM extracted_data
                    WHERE employee_id = ?
                    AND item_label = 'SECTION'
                    AND item_value IS NOT NULL 
                    AND item_value != '' 
                    AND item_value != 'None'
                    ORDER BY created_at DESC 
                    LIMIT 1
                """, (emp_id,))
                
                section_result = cursor.fetchone()
                
                if section_result:
                    print(f"      📋 {emp_id}: SECTION fallback = {section_result[0]}")
                else:
                    print(f"      ❌ {emp_id}: No SECTION either")
        
        # 6. FINAL ASSESSMENT
        print(f"\n6. 🎯 FINAL ASSESSMENT:")
        print("=" * 50)
        
        if has_department and has_employee_name:
            print(f"✅ SCHEMA UPDATED CORRECTLY")
            print(f"   📊 tracker_results has department and employee_name columns")
            
            if total_records > 0:
                if has_department:
                    cursor.execute("""
                        SELECT COUNT(*) FROM tracker_results
                        WHERE department IS NOT NULL AND department != ''
                    """)
                    records_with_dept = cursor.fetchone()[0]
                    coverage = (records_with_dept / total_records) * 100
                    
                    if coverage >= 90:
                        print(f"✅ EXCELLENT DEPARTMENT COVERAGE: {coverage:.1f}%")
                    elif coverage >= 70:
                        print(f"✅ GOOD DEPARTMENT COVERAGE: {coverage:.1f}%")
                    else:
                        print(f"⚠️ LOW DEPARTMENT COVERAGE: {coverage:.1f}%")
                        print(f"   🔧 May need to run tracker population again")
            else:
                print(f"⚠️ No tracker records to test")
        else:
            print(f"❌ SCHEMA UPDATE INCOMPLETE")
            if not has_department:
                print(f"   🔧 Missing department column")
            if not has_employee_name:
                print(f"   🔧 Missing employee_name column")
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"   1. Run a new payroll audit to test department population")
        print(f"   2. Verify that new tracker records include departments")
        print(f"   3. Check Bank Adviser Tracker functionality")
        
        conn.close()
        return has_department and has_employee_name
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_department_fix()
    if success:
        print(f"\n✅ DEPARTMENT FIX IMPLEMENTATION SUCCESSFUL")
    else:
        print(f"\n❌ DEPARTMENT FIX IMPLEMENTATION INCOMPLETE")
    sys.exit(0 if success else 1)
