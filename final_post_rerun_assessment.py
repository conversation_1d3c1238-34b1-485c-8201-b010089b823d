#!/usr/bin/env python3
"""
Final Post-Rerun Assessment
Complete assessment after the extraction job was re-run
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def final_assessment():
    """Final comprehensive assessment after re-run"""
    print("🎯 FINAL POST-RERUN ASSESSMENT")
    print("=" * 70)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Current tracker session
        tracker_session = "audit_session_1751122785_5479febb"
        
        # Latest extraction sessions
        latest_sessions = [
            "audit_session_1751146680_5d92ac57",
            "audit_session_1751146680_1a89e34e"
        ]
        
        print(f"📊 TRACKER SESSION: {tracker_session}")
        print(f"📊 LATEST EXTRACTION SESSIONS: {len(latest_sessions)} sessions")
        
        # 1. GET TRACKER EMPLOYEES
        cursor.execute("""
            SELECT DISTINCT employee_id, employee_name 
            FROM tracker_results 
            WHERE session_id = ? 
            ORDER BY employee_id
        """, (tracker_session,))
        
        tracker_employees = cursor.fetchall()
        print(f"\n1. 📋 TRACKER EMPLOYEES: {len(tracker_employees)} total")
        
        # 2. CHECK DEPARTMENT COVERAGE AFTER RE-RUN
        print(f"\n2. 📋 DEPARTMENT COVERAGE ANALYSIS:")
        
        found_in_employees = 0
        found_in_extracted = 0
        still_missing = []
        
        for emp_id, emp_name in tracker_employees:
            # Check employees table
            cursor.execute("""
                SELECT department FROM employees
                WHERE employee_id = ?
                AND department IS NOT NULL 
                AND department != '' 
                AND department != 'None'
                ORDER BY created_at DESC LIMIT 1
            """, (emp_id,))
            
            emp_result = cursor.fetchone()
            if emp_result:
                found_in_employees += 1
                continue
            
            # Check extracted_data table (latest sessions)
            cursor.execute("""
                SELECT item_value FROM extracted_data
                WHERE employee_id = ?
                AND item_label = 'DEPARTMENT'
                AND item_value IS NOT NULL 
                AND item_value != ''
                AND item_value != 'None'
                ORDER BY created_at DESC LIMIT 1
            """, (emp_id,))
            
            ext_result = cursor.fetchone()
            if ext_result:
                found_in_extracted += 1
                continue
            
            # Still missing
            still_missing.append((emp_id, emp_name))
        
        total_found = found_in_employees + found_in_extracted
        success_rate = (total_found / len(tracker_employees)) * 100
        
        print(f"   ✅ Found in employees table: {found_in_employees}")
        print(f"   ✅ Found in extracted_data: {found_in_extracted}")
        print(f"   ✅ Total with departments: {total_found}/{len(tracker_employees)} ({success_rate:.1f}%)")
        print(f"   ❌ Still missing: {len(still_missing)}")
        
        # 3. ANALYZE STILL MISSING EMPLOYEES
        if still_missing:
            print(f"\n3. 📋 STILL MISSING EMPLOYEES ({len(still_missing)}):")
            
            for emp_id, emp_name in still_missing:
                print(f"\n   ❌ {emp_id}: {emp_name}")
                
                # Check if they exist in latest extraction sessions
                for session in latest_sessions:
                    cursor.execute("""
                        SELECT COUNT(*), 
                               COUNT(CASE WHEN item_label = 'DEPARTMENT' THEN 1 END) as dept_count
                        FROM extracted_data 
                        WHERE session_id = ? AND employee_id = ?
                    """, (session, emp_id))
                    
                    result = cursor.fetchone()
                    if result and result[0] > 0:
                        total_records, dept_count = result
                        print(f"      📋 {session[:20]}...: {total_records} records, {dept_count} dept records")
                        
                        if dept_count == 0:
                            # Check what labels they do have
                            cursor.execute("""
                                SELECT DISTINCT item_label
                                FROM extracted_data 
                                WHERE session_id = ? AND employee_id = ?
                                ORDER BY item_label
                            """, (session, emp_id))
                            
                            labels = [row[0] for row in cursor.fetchall()]
                            print(f"         Labels: {', '.join(labels[:5])}")
                            if len(labels) > 5:
                                print(f"         ... and {len(labels) - 5} more")
                    else:
                        print(f"      ❌ {session[:20]}...: Not found")
        
        # 4. EXTRACTION IMPROVEMENT ANALYSIS
        print(f"\n4. 📋 EXTRACTION IMPROVEMENT ANALYSIS:")
        
        # Compare old vs new extraction sessions
        old_sessions = [
            "audit_session_1751142713_e93d2d66",
            "audit_session_1751142713_0a2b64fa"
        ]
        
        print(f"\n   📊 BEFORE RE-RUN (old sessions):")
        for session in old_sessions:
            cursor.execute("""
                SELECT COUNT(DISTINCT employee_id) as employees,
                       COUNT(CASE WHEN item_label = 'DEPARTMENT' THEN 1 END) as dept_records
                FROM extracted_data 
                WHERE session_id = ?
            """, (session,))
            
            result = cursor.fetchone()
            if result:
                employees, dept_records = result
                print(f"      {session[:20]}...: {employees} employees, {dept_records} dept records")
        
        print(f"\n   📊 AFTER RE-RUN (new sessions):")
        for session in latest_sessions:
            cursor.execute("""
                SELECT COUNT(DISTINCT employee_id) as employees,
                       COUNT(CASE WHEN item_label = 'DEPARTMENT' THEN 1 END) as dept_records
                FROM extracted_data 
                WHERE session_id = ?
            """, (session,))
            
            result = cursor.fetchone()
            if result:
                employees, dept_records = result
                print(f"      {session[:20]}...: {employees} employees, {dept_records} dept records")
        
        # 5. FINAL RECOMMENDATIONS
        print(f"\n5. 🎯 FINAL RECOMMENDATIONS:")
        print("=" * 50)
        
        if success_rate >= 95:
            print(f"   ✅ EXCELLENT: {success_rate:.1f}% success rate")
            print(f"   ✅ Department lookup is working correctly")
            print(f"   ✅ Tracker population should work for {total_found}/{len(tracker_employees)} employees")
        elif success_rate >= 90:
            print(f"   ✅ GOOD: {success_rate:.1f}% success rate")
            print(f"   ⚠️ {len(still_missing)} employees still need manual investigation")
        else:
            print(f"   ⚠️ NEEDS IMPROVEMENT: {success_rate:.1f}% success rate")
            print(f"   ❌ {len(still_missing)} employees still missing department data")
        
        if still_missing:
            print(f"\n   📋 NEXT STEPS FOR MISSING EMPLOYEES:")
            print(f"      1. Check if these employees have different PDF layouts")
            print(f"      2. Verify department information exists in source PDFs")
            print(f"      3. Consider manual department assignment for these {len(still_missing)} employees")
            print(f"      4. Check if they belong to different organizational units")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Assessment failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = final_assessment()
    sys.exit(0 if success else 1)
