renderer.js:124 ❌ Error during payroll audit: Error: Process exited with code null
    at window.startPayrollAuditProcess (renderer.js:120:13)
window.startPayrollAuditProcess	@	renderer.js:124
renderer.js:138 ❌ Detailed error: Process exited with code null
window.startPayrollAuditProcess	@	renderer.js:138
renderer.js:124 ❌ Error during payroll audit: Error: Process exited with code null
    at window.startPayrollAuditProcess (renderer.js:120:13)
window.startPayrollAuditProcess	@	renderer.js:124
renderer.js:138 ❌ Detailed error: Process exited with code null
window.startPayrollAuditProcess	@	renderer.js:138
2
renderer.js:1924 ❌ Error during InteractivePreReporting initialization: ReferenceError: beautifulContainer is not defined
    at initializeInteractivePreReporting (renderer.js:1887:7)
    at loadPreReportingUIFromDatabase (renderer.js:1415:5)
initializeInteractivePreReporting	@	renderer.js:1924
2
renderer.js:2115 ❌ Tracker population failed: Using session audit_session_1751122785_5479febb with 69 tracker items
populateTrackerTablesRedesigned	@	renderer.js:2115
22
renderer.js:1317 ❌ Pre-reporting API returned error: Process exited with code null
loadPreReportingUIFromDatabase	@	renderer.js:1317
2
renderer.js:2115 ❌ Tracker population failed: Process exited with code null
populateTrackerTablesRedesigned	@	renderer.js:2115
2
renderer.js:1924 ❌ Error during InteractivePreReporting initialization: ReferenceError: beautifulContainer is not defined
    at initializeInteractivePreReporting (renderer.js:1887:7)
    at loadPreReportingUIFromDatabase (renderer.js:1415:5)
    at async activateRedesignedPreReporting (renderer.js:2084:5)
initializeInteractivePreReporting	@	renderer.js:1924
9
renderer.js:2115 ❌ Tracker population failed: database is locked
populateTrackerTablesRedesigned	@	renderer.js:2115
10
renderer.js:1924 ❌ Error during InteractivePreReporting initialization: ReferenceError: beautifulContainer is not defined
    at initializeInteractivePreReporting (renderer.js:1887:7)
    at loadPreReportingUIFromDatabase (renderer.js:1415:5)
    at async activateRedesignedPreReporting (renderer.js:2084:5)
initializeInteractivePreReporting	@	renderer.js:1924
﻿

