#!/usr/bin/env python3
"""
Check Current Session Names
Find the correct session and check name extraction issues
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_current_session_names():
    """Check current session and name issues"""
    print("🔍 CHECKING CURRENT SESSION AND NAME ISSUES")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. FIND LATEST SESSION
        print("1. 📋 FINDING LATEST EXTRACTION SESSION:")
        
        cursor.execute("""
            SELECT session_id, COUNT(DISTINCT employee_id) as employees
            FROM extracted_data
            GROUP BY session_id
            ORDER BY MAX(created_at) DESC
            LIMIT 5
        """)
        
        sessions = cursor.fetchall()
        
        if not sessions:
            print("   ❌ No extraction sessions found")
            return False
        
        latest_session = sessions[0][0]
        
        for session_id, emp_count in sessions:
            print(f"   📊 {session_id}: {emp_count} employees")
        
        print(f"\n   🎯 Using latest session: {latest_session}")
        
        # 2. CHECK PW0101 SPECIFICALLY
        print(f"\n2. 📋 CHECKING PW0101 NAME ISSUE:")
        
        cursor.execute("""
            SELECT item_label, item_value
            FROM extracted_data
            WHERE session_id = ? AND employee_id = 'PW0101'
            AND item_label IN ('EMPLOYEE NAME', 'GHANA CARD ID', 'JOB TITLE')
            ORDER BY item_label
        """, (latest_session,))
        
        pw0101_data = cursor.fetchall()
        
        if pw0101_data:
            print(f"   📊 PW0101 key fields:")
            for label, value in pw0101_data:
                print(f"      {label}: {value}")
                if label == 'EMPLOYEE NAME' and 'GHANA' in str(value).upper():
                    print(f"         🚨 NAME CORRUPTION CONFIRMED!")
        else:
            print(f"   ❌ PW0101 not found in latest session")
        
        # 3. CHECK FOR NAME CORRUPTION PATTERNS
        print(f"\n3. 📋 CHECKING NAME CORRUPTION PATTERNS:")
        
        # Check for Ghana Card in names
        cursor.execute("""
            SELECT employee_id, item_value
            FROM extracted_data
            WHERE session_id = ? 
            AND item_label = 'EMPLOYEE NAME'
            AND (item_value LIKE '%GHANA%' OR item_value LIKE '%CARD%')
            LIMIT 10
        """, (latest_session,))
        
        corrupted_names = cursor.fetchall()
        
        if corrupted_names:
            print(f"   🚨 FOUND {len(corrupted_names)} CORRUPTED NAMES:")
            for emp_id, name in corrupted_names:
                print(f"      {emp_id}: {name}")
        else:
            print(f"   ✅ No Ghana Card corruption in names")
        
        # 4. CHECK FOR JOB TITLES IN NAMES
        print(f"\n4. 📋 CHECKING JOB TITLES IN NAMES:")
        
        cursor.execute("""
            SELECT employee_id, item_value
            FROM extracted_data
            WHERE session_id = ? 
            AND item_label = 'EMPLOYEE NAME'
            AND (
                item_value LIKE '%OFFICER%' OR 
                item_value LIKE '%MANAGER%' OR 
                item_value LIKE '%RETIRED%' OR
                item_value LIKE '%MINISTER%' OR
                item_value LIKE '%DIRECTOR%'
            )
            LIMIT 10
        """, (latest_session,))
        
        job_title_names = cursor.fetchall()
        
        if job_title_names:
            print(f"   🚨 FOUND {len(job_title_names)} JOB TITLES IN NAMES:")
            for emp_id, name in job_title_names:
                print(f"      {emp_id}: {name}")
        else:
            print(f"   ✅ No job titles in names")
        
        # 5. SAMPLE RANDOM NAMES FOR QUALITY CHECK
        print(f"\n5. 📋 SAMPLE NAME QUALITY CHECK:")
        
        cursor.execute("""
            SELECT employee_id, item_value
            FROM extracted_data
            WHERE session_id = ? 
            AND item_label = 'EMPLOYEE NAME'
            ORDER BY RANDOM()
            LIMIT 10
        """, (latest_session,))
        
        sample_names = cursor.fetchall()
        
        print(f"   📊 Random sample of names:")
        for emp_id, name in sample_names:
            # Check if name looks proper
            if name and len(name.split()) >= 2 and not any(word in name.upper() for word in ['CARD', 'ID', 'NO.', 'OFFICER', 'MANAGER']):
                status = "✅"
            else:
                status = "❌"
            print(f"      {status} {emp_id}: {name}")
        
        # 6. CHECK FIELD ALIGNMENT FOR PROBLEMATIC EMPLOYEES
        print(f"\n6. 📋 CHECKING FIELD ALIGNMENT:")
        
        problematic_employees = [emp_id for emp_id, _ in corrupted_names + job_title_names]
        
        if problematic_employees:
            print(f"   🔍 Analyzing field alignment for problematic employees:")
            
            for emp_id in problematic_employees[:3]:  # Check first 3
                print(f"\n      📊 {emp_id} - All fields:")
                
                cursor.execute("""
                    SELECT item_label, item_value
                    FROM extracted_data
                    WHERE session_id = ? AND employee_id = ?
                    AND item_label IN ('EMPLOYEE NAME', 'EMPLOYEE NO.', 'JOB TITLE', 'GHANA CARD ID', 'SSF NO.')
                    ORDER BY item_label
                """, (latest_session, emp_id))
                
                all_fields = cursor.fetchall()
                for label, value in all_fields:
                    print(f"         {label}: {value}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_current_session_names()
    sys.exit(0 if success else 1)
