#!/usr/bin/env python3
"""
Proper Department Assessment
Comprehensive analysis of where department data should be and why it's missing
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def comprehensive_department_assessment():
    """Comprehensive assessment of department data storage and retrieval"""
    print("🔍 COMPREHENSIVE DEPARTMENT DATA ASSESSMENT")
    print("=" * 70)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Current tracker session
        tracker_session = "audit_session_1751122785_5479febb"
        
        print(f"📊 ANALYZING SESSION: {tracker_session}")
        print("=" * 70)
        
        # 1. GET ALL TRACKER EMPLOYEES
        cursor.execute("""
            SELECT DISTINCT employee_id, employee_name 
            FROM tracker_results 
            WHERE session_id = ? 
            ORDER BY employee_id
        """, (tracker_session,))
        
        tracker_employees = cursor.fetchall()
        print(f"\n1. 📋 TRACKER EMPLOYEES ({len(tracker_employees)} total):")
        
        # Show sample of tracker employees
        for i, (emp_id, emp_name) in enumerate(tracker_employees[:10]):
            print(f"   {emp_id}: {emp_name}")
        if len(tracker_employees) > 10:
            print(f"   ... and {len(tracker_employees) - 10} more")
        
        # 2. CHECK WHERE THESE EMPLOYEES EXIST WITH DEPARTMENT DATA
        print(f"\n2. 📋 DEPARTMENT DATA LOCATIONS:")
        
        # Check employees table across ALL sessions
        department_found = {}
        for emp_id, emp_name in tracker_employees:
            cursor.execute("""
                SELECT session_id, department, created_at
                FROM employees 
                WHERE employee_id = ? 
                AND department IS NOT NULL 
                AND department != '' 
                AND department != 'None'
                ORDER BY created_at DESC
            """, (emp_id,))
            
            results = cursor.fetchall()
            if results:
                department_found[emp_id] = results[0]  # Most recent
        
        print(f"   📊 Found departments for {len(department_found)}/{len(tracker_employees)} employees")
        
        # Show sample of found departments
        sample_found = list(department_found.items())[:5]
        for emp_id, (session_id, dept, created_at) in sample_found:
            print(f"   ✅ {emp_id}: {dept} (from {session_id[:20]}...)")
        
        # 3. IDENTIFY MISSING EMPLOYEES
        missing_employees = []
        for emp_id, emp_name in tracker_employees:
            if emp_id not in department_found:
                missing_employees.append((emp_id, emp_name))
        
        print(f"\n3. 📋 MISSING DEPARTMENT DATA ({len(missing_employees)} employees):")
        for emp_id, emp_name in missing_employees:
            print(f"   ❌ {emp_id}: {emp_name}")
        
        # 4. CHECK IF MISSING EMPLOYEES EXIST IN EMPLOYEES TABLE AT ALL
        if missing_employees:
            print(f"\n4. 📋 CHECKING IF MISSING EMPLOYEES EXIST IN EMPLOYEES TABLE:")
            
            for emp_id, emp_name in missing_employees:
                cursor.execute("""
                    SELECT session_id, department, created_at
                    FROM employees 
                    WHERE employee_id = ?
                    ORDER BY created_at DESC
                    LIMIT 3
                """, (emp_id,))
                
                all_records = cursor.fetchall()
                if all_records:
                    print(f"   📋 {emp_id} exists in employees table:")
                    for session_id, dept, created_at in all_records:
                        dept_status = dept if dept and dept != 'None' else "NULL/EMPTY"
                        print(f"      {session_id[:20]}...: {dept_status} ({created_at})")
                else:
                    print(f"   ❌ {emp_id}: NOT FOUND in employees table at all")
        
        # 5. CHECK EXTRACTED_DATA FOR DEPARTMENT INFORMATION
        print(f"\n5. 📋 CHECKING EXTRACTED_DATA FOR DEPARTMENT INFO:")
        
        # Check if any session has department data in extracted_data
        cursor.execute("""
            SELECT DISTINCT session_id, COUNT(*) as count
            FROM extracted_data 
            WHERE (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%' 
                   OR item_label LIKE '%SECTION%' OR item_label LIKE '%DIVISION%')
            AND item_value IS NOT NULL AND item_value != ''
            GROUP BY session_id
            ORDER BY count DESC
            LIMIT 5
        """)
        
        dept_sessions = cursor.fetchall()
        if dept_sessions:
            print(f"   📊 Sessions with department data in extracted_data:")
            for session_id, count in dept_sessions:
                print(f"      {session_id}: {count} department records")
                
                # Sample department data from this session
                cursor.execute("""
                    SELECT employee_id, item_label, item_value
                    FROM extracted_data 
                    WHERE session_id = ?
                    AND (item_label LIKE '%DEPARTMENT%' OR item_label LIKE '%DEPT%')
                    AND item_value IS NOT NULL AND item_value != ''
                    LIMIT 3
                """, (session_id,))
                
                samples = cursor.fetchall()
                for emp_id, label, value in samples:
                    print(f"         {emp_id}: {label} = {value}")
        else:
            print(f"   ❌ No department data found in extracted_data")
        
        # 6. FINAL ASSESSMENT AND RECOMMENDATIONS
        print(f"\n6. 🎯 FINAL ASSESSMENT:")
        print("=" * 50)
        
        success_rate = len(department_found) / len(tracker_employees) * 100
        print(f"   📊 Department lookup success rate: {success_rate:.1f}%")
        print(f"   ✅ Employees with departments: {len(department_found)}")
        print(f"   ❌ Employees without departments: {len(missing_employees)}")
        
        if len(department_found) > 0:
            # Find the most common session with department data
            sessions = [data[0] for data in department_found.values()]
            from collections import Counter
            session_counts = Counter(sessions)
            most_common_session = session_counts.most_common(1)[0]
            
            print(f"\n   🎯 RECOMMENDED SOLUTION:")
            print(f"      Most employees have departments in session: {most_common_session[0]}")
            print(f"      ({most_common_session[1]} employees)")
            print(f"      Use this session as the primary source for department lookup")
        
        if missing_employees:
            print(f"\n   ⚠️ MISSING EMPLOYEES NEED INVESTIGATION:")
            print(f"      These {len(missing_employees)} employees have no department data anywhere")
            print(f"      Check if they were properly extracted from the original payroll data")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Assessment failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = comprehensive_department_assessment()
    sys.exit(0 if success else 1)
