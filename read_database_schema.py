#!/usr/bin/env python3
"""
Read Database Schema
Read the actual database schema to understand the structure
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    return r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"

def read_database_schema():
    """Read the complete database schema"""
    print("🔍 READING DATABASE SCHEMA")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. GET ALL TABLES
        print("1. 📋 ALL TABLES IN DATABASE:")
        
        cursor.execute("""
            SELECT name, type FROM sqlite_master 
            WHERE type='table' 
            ORDER BY name
        """)
        
        tables = cursor.fetchall()
        
        for table_name, table_type in tables:
            print(f"   📊 {table_name}")
        
        print(f"\n   Total tables: {len(tables)}")
        
        # 2. GET SCHEMA FOR EACH IMPORTANT TABLE
        important_tables = [
            'extracted_data', 'employees', 'comparison_results', 
            'tracker_results', 'payroll_data', 'audit_results'
        ]
        
        print(f"\n2. 📋 SCHEMA FOR IMPORTANT TABLES:")
        
        for table_name in important_tables:
            # Check if table exists
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            
            if cursor.fetchone():
                print(f"\n   📊 {table_name.upper()} TABLE:")
                
                # Get schema
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                for col_id, col_name, col_type, not_null, default_val, primary_key in columns:
                    pk_marker = " (PK)" if primary_key else ""
                    null_marker = " NOT NULL" if not_null else ""
                    default_marker = f" DEFAULT {default_val}" if default_val else ""
                    print(f"      {col_name}: {col_type}{pk_marker}{null_marker}{default_marker}")
                
                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                print(f"      📊 Rows: {row_count:,}")
                
                # Get sample data for key tables
                if table_name in ['extracted_data', 'employees'] and row_count > 0:
                    print(f"      📋 Sample data:")
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    sample_rows = cursor.fetchall()
                    
                    for i, row in enumerate(sample_rows):
                        print(f"         Row {i+1}: {str(row)[:100]}...")
            else:
                print(f"\n   ❌ {table_name} table does not exist")
        
        # 3. CHECK FOR PAYROLL-SPECIFIC TABLES
        print(f"\n3. 📋 PAYROLL-SPECIFIC TABLES:")
        
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' 
            AND (name LIKE '%payroll%' OR name LIKE '%audit%' OR name LIKE '%comparison%')
            ORDER BY name
        """)
        
        payroll_tables = cursor.fetchall()
        
        if payroll_tables:
            for table_name, in payroll_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                print(f"   📊 {table_name}: {row_count:,} rows")
        else:
            print(f"   ❌ No payroll-specific tables found")
        
        # 4. CHECK RECENT DATA ACTIVITY
        print(f"\n4. 📋 RECENT DATA ACTIVITY:")
        
        # Check tables with created_at or timestamp columns
        for table_name, _ in tables:
            try:
                # Check if table has created_at column
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                has_created_at = any(col[1] == 'created_at' for col in columns)
                has_timestamp = any('time' in col[1].lower() for col in columns)
                
                if has_created_at or has_timestamp:
                    # Get latest activity
                    time_col = 'created_at' if has_created_at else next(col[1] for col in columns if 'time' in col[1].lower())
                    
                    cursor.execute(f"SELECT MAX({time_col}) FROM {table_name}")
                    latest_time = cursor.fetchone()[0]
                    
                    if latest_time:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        total_rows = cursor.fetchone()[0]
                        
                        print(f"   📊 {table_name}: {total_rows:,} rows, latest: {latest_time}")
            except:
                pass  # Skip tables with issues
        
        # 5. FIND THE ACTUAL PAYROLL DATA
        print(f"\n5. 📋 FINDING ACTUAL PAYROLL DATA:")
        
        # Look for tables with employee_id and around 5919 records
        for table_name, _ in tables:
            try:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                has_employee_id = any(col[1] == 'employee_id' for col in columns)
                
                if has_employee_id:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    total_rows = cursor.fetchone()[0]
                    
                    cursor.execute(f"SELECT COUNT(DISTINCT employee_id) FROM {table_name}")
                    unique_employees = cursor.fetchone()[0]
                    
                    print(f"   📊 {table_name}:")
                    print(f"      Total rows: {total_rows:,}")
                    print(f"      Unique employees: {unique_employees:,}")
                    
                    if unique_employees >= 5800:
                        print(f"      ✅ POTENTIAL COMPLETE DATA!")
                        
                        # Check for our 5 target employees
                        cursor.execute(f"""
                            SELECT employee_id FROM {table_name}
                            WHERE employee_id IN ('PW0085', 'PW0101', 'COP2894', 'COP3195', 'COP3617')
                        """)
                        
                        found_employees = cursor.fetchall()
                        print(f"      📋 Target employees found: {len(found_employees)}/5")
                        for emp_id, in found_employees:
                            print(f"         ✅ {emp_id}")
            except:
                pass  # Skip tables with issues
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Schema reading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = read_database_schema()
    sys.exit(0 if success else 1)
