#!/usr/bin/env python3
"""
Check July and June Period Extraction
Analyze how many people were extracted for July and June periods
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_july_june_extraction():
    """Check extraction counts for July and June periods"""
    print("🔍 CHECKING JULY AND JUNE PERIOD EXTRACTION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. CHECK EMPLOYEES TABLE FOR PERIOD INFORMATION
        print(f"1. 📋 EMPLOYEES TABLE PERIOD ANALYSIS:")
        
        # Check if there's a period column or period information
        cursor.execute("PRAGMA table_info(employees)")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"   📊 Columns in employees table: {', '.join(columns)}")
        
        if 'period_type' in columns:
            cursor.execute("""
                SELECT period_type, COUNT(DISTINCT employee_id) as employees
                FROM employees
                GROUP BY period_type
                ORDER BY employees DESC
            """)
            
            period_counts = cursor.fetchall()
            print(f"\n   📊 Employees by period_type:")
            for period, count in period_counts:
                print(f"      {period}: {count} employees")
        
        # 2. CHECK EXTRACTED_DATA FOR PERIOD INFORMATION
        print(f"\n2. 📋 EXTRACTED_DATA PERIOD ANALYSIS:")
        
        # Check for period-related labels
        cursor.execute("""
            SELECT DISTINCT item_label
            FROM extracted_data
            WHERE item_label LIKE '%PERIOD%' OR item_label LIKE '%MONTH%' OR item_label LIKE '%YEAR%'
            ORDER BY item_label
        """)
        
        period_labels = cursor.fetchall()
        if period_labels:
            print(f"   📊 Period-related labels found:")
            for label in period_labels:
                print(f"      {label[0]}")
                
                # Sample values for this label
                cursor.execute("""
                    SELECT DISTINCT item_value, COUNT(*) as count
                    FROM extracted_data
                    WHERE item_label = ?
                    AND item_value IS NOT NULL AND item_value != ''
                    GROUP BY item_value
                    ORDER BY count DESC
                    LIMIT 10
                """, (label[0],))
                
                values = cursor.fetchall()
                for value, count in values:
                    print(f"         {value}: {count} records")
        else:
            print(f"   ❌ No period-related labels found")
        
        # 3. CHECK SESSION INFORMATION FOR PERIODS
        print(f"\n3. 📋 SESSION-BASED PERIOD ANALYSIS:")
        
        # Check audit_sessions table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audit_sessions'")
        if cursor.fetchone():
            cursor.execute("""
                SELECT session_id, created_at, 
                       CASE 
                           WHEN session_id LIKE '%june%' OR session_id LIKE '%06%' THEN 'JUNE'
                           WHEN session_id LIKE '%july%' OR session_id LIKE '%07%' THEN 'JULY'
                           ELSE 'OTHER'
                       END as period_guess
                FROM audit_sessions
                ORDER BY created_at DESC
            """)
            
            sessions = cursor.fetchall()
            if sessions:
                print(f"   📊 Audit sessions:")
                for session_id, created_at, period_guess in sessions:
                    print(f"      {session_id}: {created_at} ({period_guess})")
        
        # 4. ANALYZE EXTRACTION SESSIONS BY DATE/NAME PATTERNS
        print(f"\n4. 📋 EXTRACTION SESSION ANALYSIS:")
        
        # Get all extraction sessions with employee counts
        cursor.execute("""
            SELECT session_id, 
                   COUNT(DISTINCT employee_id) as employees,
                   MIN(created_at) as first_record,
                   MAX(created_at) as last_record,
                   CASE 
                       WHEN session_id LIKE '%june%' OR session_id LIKE '%06%' THEN 'JUNE'
                       WHEN session_id LIKE '%july%' OR session_id LIKE '%07%' THEN 'JULY'
                       WHEN created_at LIKE '2025-06%' THEN 'JUNE_BY_DATE'
                       WHEN created_at LIKE '2025-07%' THEN 'JULY_BY_DATE'
                       ELSE 'UNKNOWN'
                   END as period_classification
            FROM extracted_data
            GROUP BY session_id
            ORDER BY last_record DESC
        """)
        
        extraction_sessions = cursor.fetchall()
        
        june_total = 0
        july_total = 0
        unknown_total = 0
        
        print(f"\n   📊 Extraction sessions by period:")
        for session_id, employees, first_record, last_record, period_class in extraction_sessions:
            print(f"\n      📋 {session_id}:")
            print(f"         Employees: {employees}")
            print(f"         Period: {period_class}")
            print(f"         Date range: {first_record} to {last_record}")
            
            if period_class in ['JUNE', 'JUNE_BY_DATE']:
                june_total += employees
            elif period_class in ['JULY', 'JULY_BY_DATE']:
                july_total += employees
            else:
                unknown_total += employees
        
        # 5. CHECK FOR SPECIFIC JULY/JUNE INDICATORS IN DATA
        print(f"\n5. 📋 SEARCHING FOR JULY/JUNE INDICATORS IN DATA:")
        
        # Search for month indicators in extracted data
        month_searches = [
            ('JUNE', ['june', 'JUNE', '06', '2025-06']),
            ('JULY', ['july', 'JULY', '07', '2025-07'])
        ]
        
        for month_name, search_terms in month_searches:
            print(f"\n   🔍 {month_name} indicators:")
            
            total_month_employees = set()
            
            for term in search_terms:
                cursor.execute("""
                    SELECT DISTINCT employee_id
                    FROM extracted_data
                    WHERE item_value LIKE ?
                """, (f'%{term}%',))
                
                employees = cursor.fetchall()
                if employees:
                    employee_ids = [emp[0] for emp in employees]
                    total_month_employees.update(employee_ids)
                    print(f"      '{term}': {len(employees)} employees")
            
            if total_month_employees:
                print(f"      📊 Total unique {month_name} employees: {len(total_month_employees)}")
        
        # 6. FINAL SUMMARY
        print(f"\n6. 🎯 FINAL EXTRACTION SUMMARY:")
        print("=" * 50)
        
        print(f"   📊 ESTIMATED EXTRACTION COUNTS:")
        print(f"      JUNE period: {june_total} employees")
        print(f"      JULY period: {july_total} employees") 
        print(f"      Unknown/Other: {unknown_total} employees")
        
        # Get total unique employees across all sessions
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id) as total_unique
            FROM extracted_data
        """)
        
        total_unique = cursor.fetchone()[0]
        print(f"      Total unique employees: {total_unique}")
        
        # Check if we have both periods for same employees
        if june_total > 0 and july_total > 0:
            print(f"\n   📋 PERIOD COVERAGE ANALYSIS:")
            print(f"      Both periods may be extracted for the same employee base")
            print(f"      This suggests dual-period payroll processing")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_july_june_extraction()
    sys.exit(0 if success else 1)
