#!/usr/bin/env python3
"""
Check PW Department Extraction
Check if PW employees have department data that's not being recognized due to formatting
"""

import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_pw_department_extraction():
    """Check PW department extraction issues"""
    print("🔍 CHECKING PW DEPARTMENT EXTRACTION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Latest session
        latest_session = "audit_session_1751148955_7321e46d"
        
        # 1. CHECK ALL PW EMPLOYEES IN LATEST SESSION
        print("1. 📋 ALL PW EMPLOYEES IN LATEST SESSION:")
        
        cursor.execute("""
            SELECT DISTINCT employee_id
            FROM extracted_data
            WHERE session_id = ? AND employee_id LIKE 'PW%'
            ORDER BY employee_id
        """, (latest_session,))
        
        pw_employees = [row[0] for row in cursor.fetchall()]
        print(f"   📊 Found {len(pw_employees)} PW employees")
        
        # Show first 10
        for emp_id in pw_employees[:10]:
            print(f"      {emp_id}")
        if len(pw_employees) > 10:
            print(f"      ... and {len(pw_employees) - 10} more")
        
        # 2. CHECK SPECIFIC PW EMPLOYEES FOR ALL DATA
        print(f"\n2. 📋 DETAILED CHECK OF SPECIFIC PW EMPLOYEES:")
        
        target_employees = ['PW0085', 'PW0101']
        
        for emp_id in target_employees:
            print(f"\n   🔍 {emp_id} - ALL EXTRACTED DATA:")
            
            cursor.execute("""
                SELECT item_label, item_value
                FROM extracted_data
                WHERE session_id = ? AND employee_id = ?
                ORDER BY item_label
            """, (latest_session, emp_id))
            
            all_data = cursor.fetchall()
            
            department_found = False
            section_found = False
            
            for label, value in all_data:
                print(f"      {label}: {value}")
                
                # Check for department-like labels
                if 'DEPARTMENT' in label.upper():
                    department_found = True
                    print(f"         🎯 DEPARTMENT FOUND!")
                elif 'SECTION' in label.upper():
                    section_found = True
                    print(f"         🎯 SECTION FOUND!")
                elif any(word in label.upper() for word in ['DEPT', 'DIVISION', 'UNIT', 'AREA']):
                    print(f"         🎯 DEPARTMENT-LIKE LABEL!")
            
            if not department_found and not section_found:
                print(f"      ❌ No department or section data found")
            
            # Check if there are any labels with special characters or formatting issues
            print(f"\n      🔍 CHECKING FOR FORMATTING ISSUES:")
            
            for label, value in all_data:
                # Check for labels that might be department but with special chars
                if any(char in label for char in ['-', '/', '\\', '&', '.']):
                    if any(word in label.upper() for word in ['DEPARTMENT', 'DEPT', 'AREA', 'SECTION']):
                        print(f"         🎯 POTENTIAL DEPARTMENT WITH SPECIAL CHARS: {label} = {value}")
                
                # Check for values that look like department names
                if value and isinstance(value, str):
                    if any(word in value.upper() for word in ['AREA', 'DEPARTMENT', 'MINISTERS', 'STAFF', 'KASOA', 'NSAWAM']):
                        print(f"         🎯 POTENTIAL DEPARTMENT VALUE: {label} = {value}")
        
        # 3. CHECK ALL LABELS THAT MIGHT CONTAIN DEPARTMENT INFO FOR PW EMPLOYEES
        print(f"\n3. 📋 ALL DEPARTMENT-LIKE LABELS FOR PW EMPLOYEES:")
        
        cursor.execute("""
            SELECT DISTINCT item_label, COUNT(*) as count
            FROM extracted_data
            WHERE session_id = ? 
            AND employee_id LIKE 'PW%'
            AND (
                item_label LIKE '%DEPARTMENT%' OR 
                item_label LIKE '%DEPT%' OR 
                item_label LIKE '%SECTION%' OR 
                item_label LIKE '%AREA%' OR 
                item_label LIKE '%DIVISION%' OR
                item_label LIKE '%UNIT%'
            )
            GROUP BY item_label
            ORDER BY count DESC
        """, (latest_session,))
        
        dept_labels = cursor.fetchall()
        
        if dept_labels:
            print(f"   📊 Found {len(dept_labels)} department-like labels for PW employees:")
            for label, count in dept_labels:
                print(f"      {label}: {count} PW employees")
                
                # Get sample values
                cursor.execute("""
                    SELECT DISTINCT item_value, COUNT(*) as count
                    FROM extracted_data
                    WHERE session_id = ? 
                    AND employee_id LIKE 'PW%'
                    AND item_label = ?
                    GROUP BY item_value
                    ORDER BY count DESC
                    LIMIT 5
                """, (latest_session, label))
                
                values = cursor.fetchall()
                for value, val_count in values:
                    print(f"         {value}: {val_count} employees")
        else:
            print(f"   ❌ No department-like labels found for PW employees")
        
        # 4. CHECK IF PW EMPLOYEES HAVE DIFFERENT EXTRACTION PATTERN
        print(f"\n4. 📋 PW vs COP EXTRACTION PATTERN COMPARISON:")
        
        # Compare PW vs COP department extraction
        cursor.execute("""
            SELECT 
                CASE WHEN employee_id LIKE 'PW%' THEN 'PW' ELSE 'COP' END as emp_type,
                COUNT(DISTINCT employee_id) as total_employees,
                COUNT(CASE WHEN item_label = 'DEPARTMENT' THEN 1 END) as dept_records,
                COUNT(CASE WHEN item_label = 'SECTION' THEN 1 END) as section_records
            FROM extracted_data
            WHERE session_id = ?
            AND (employee_id LIKE 'PW%' OR employee_id LIKE 'COP%')
            GROUP BY CASE WHEN employee_id LIKE 'PW%' THEN 'PW' ELSE 'COP' END
        """, (latest_session,))
        
        comparison = cursor.fetchall()
        
        for emp_type, total, dept_records, section_records in comparison:
            print(f"   📊 {emp_type} employees:")
            print(f"      Total: {total}")
            print(f"      Department records: {dept_records}")
            print(f"      Section records: {section_records}")
            
            if dept_records == 0 and section_records > 0:
                print(f"      🎯 {emp_type} employees use SECTION instead of DEPARTMENT")
            elif dept_records == 0 and section_records == 0:
                print(f"      ❌ {emp_type} employees have NO department/section data")
        
        # 5. SEARCH FOR SPECIFIC DEPARTMENT NAMES
        print(f"\n5. 📋 SEARCHING FOR SPECIFIC DEPARTMENT NAMES:")
        
        # Search for the department names mentioned
        search_terms = ['KASOA', 'NSAWAM', 'WID', 'PENSIONS', 'AREA']
        
        for term in search_terms:
            cursor.execute("""
                SELECT employee_id, item_label, item_value
                FROM extracted_data
                WHERE session_id = ? 
                AND employee_id IN ('PW0085', 'PW0101')
                AND item_value LIKE ?
            """, (latest_session, f'%{term}%'))
            
            results = cursor.fetchall()
            if results:
                print(f"   🎯 Found '{term}' in:")
                for emp_id, label, value in results:
                    print(f"      {emp_id}: {label} = {value}")
        
        # 6. FINAL ASSESSMENT
        print(f"\n6. 🎯 FINAL ASSESSMENT:")
        print("=" * 50)
        
        # Check if we found any department data for PW employees
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_id)
            FROM extracted_data
            WHERE session_id = ? 
            AND employee_id LIKE 'PW%'
            AND (item_label = 'DEPARTMENT' OR item_label = 'SECTION')
        """, (latest_session,))
        
        pw_with_dept = cursor.fetchone()[0]
        
        print(f"   📊 PW employees with department/section data: {pw_with_dept}/{len(pw_employees)}")
        
        if pw_with_dept > 0:
            print(f"   ✅ Some PW employees have department data")
            print(f"   🎯 Issue: Specific employees (PW0085, PW0101) may have formatting issues")
        else:
            print(f"   ❌ NO PW employees have department data")
            print(f"   🎯 Issue: PW employees have different PDF layout or extraction failure")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_pw_department_extraction()
    sys.exit(0 if success else 1)
